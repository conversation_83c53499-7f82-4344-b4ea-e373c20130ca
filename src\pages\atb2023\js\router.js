const router = [{
  name: 'health-atb2023-activation',
  path: '/health-atb2023-activation',
  component: () => import( /* webpackChunkName: "health-atb2023-activation" */ '@/pages/atb2023/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-origin',
  path: '/health-atb2023-origin',
  component: () => import( /* webpackChunkName: "health-atb2023-origin" */ '@/pages/atb2023/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-introduction',
  path: '/health-atb2023-introduction',
  component: () => import( /* webpackChunkName: "health-atb2023-introduction" */ '@/pages/atb2023/introduction'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-orders',
  path: '/health-atb2023-orders',
  component: () => import( /* webpackChunkName: "health-atb2023-orders" */ '@/pages/atb2023/orders'),
  meta: {
    title: '我的预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-orderDetail',
  path: '/health-atb2023-orderDetail',
  component: () => import( /* webpackChunkName: "health-atb2023-orderDetail" */ '@/pages/atb2023/orderDetail'),
  meta: {
    title: '预约详情',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-reserve',
  path: '/health-atb2023-reserve',
  component: () => import( /* webpackChunkName: "health-atb2023-reserve" */ '@/pages/atb2023/reserve'),
  meta: {
    title: '权益预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-reserveArrange',
  path: '/health-atb2023-reserveArrange',
  component: () => import( /* webpackChunkName: "health-atb2023-reserveArrange" */ '@/pages/atb2023/reserveArrange'),
  meta: {
    title: '权益预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-reserveSuccess',
  path: '/health-atb2023-reserveSuccess',
  component: () => import( /* webpackChunkName: "health-atb2023-reserveSuccess" */ '@/pages/atb2023/reserveSuccess'),
  meta: {
    title: '预约成功',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-supplement',
  path: '/health-atb2023-supplement',
  component: () => import( /* webpackChunkName: "health-atb2023-supplement" */ '@/pages/atb2023/supplement'),
  meta: {
    title: '修改预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb2023-supplementSuccess',
  path: '/health-atb2023-supplementSuccess',
  component: () => import( /* webpackChunkName: "health-atb2023-supplementSuccess" */ '@/pages/atb2023/supplementSuccess'),
  meta: {
    title: '修改成功',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
},];
export default router;