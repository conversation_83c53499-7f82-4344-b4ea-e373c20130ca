import http from '../utils/http';

export default {
  // 上传图片
  upload: (params, param, config) => http.post('/api-member/v1/file/upload/oss/multipartFile?bizType=' + param.bizType + '&fieldName=' + param.fieldName, params, config),
  // OCR识别
  OCRIdCard: (params, config) => http.post('/api-config/v1/ocr/idcard', params, config),
  // 删除图片
  delFile: (params, config) => http.get('/api-member/v1/file/data/delete', params, config),
  // 健康报告上传
  uploadHealth: (params, config) => http.post('/api-member/api/v1/health/report/upload', params, config),
  // 健康报告删除
  delHealth: (params, config) => http.get('/api-member/api/v1/health/report/delete', params, config),
}