## SVG 图标组件

### 一、使用方式

1. 把 svg 图标放到目录（src/icons/svg/）中。svg文件可以从[iconfont官网](https://www.iconfont.cn/)下载

2. 在页面或组件中使用，iconName 属性为你 svg 的文件名。如下：

```jsx
<svg-icon iconName="success"></svg-icon>
```

<br/>

### 二、组件属性

| 参数      | 说明                                                     | 类型   | 备注   |
| --------- | -------------------------------------------------------- | ------ | ------ |
| iconName | svg 的类名（即：在目录 src/icons/svg/下 svg 文件的名字） | String | 必填   |
| className | svg 组件的 class 名                                      | String | 非必填 |

<br/>

### 二、组件特点

1. 每次添加新图标时只需要把svg文件放到指定目录即可，不需要额外引入。

2. svg为矢量图，放大不会模糊。

3. svg支持通过CSS的color属性改变图标的颜色。（彩色图标除外。如果颜色设置无效，手动删除svg文件中设置的颜色即可。）

4. svg素材较多，选择更灵活。推荐在[iconfont官网](https://www.iconfont.cn/)下载素材。
