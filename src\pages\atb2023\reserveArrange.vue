<template>
  <div class="arrangement">
    <atb-step :options="stepList" :stepNum="'3'"></atb-step>

    <div class="reserve_box">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>预约信息</span>
        </div>
      </div>
      <div class="info">
        <img v-if="icon" :src="icon" alt="">
        <div style="margin-left: .12rem">
          <p style="color: #222222;font-weight: 500">{{ rightName }}</p>
          <p>就诊人：{{reserveParams.bindName}}</p>
          <p>手机号：{{reserveParams.phoneNo}}</p>
          <p v-if="reserveParams.hospitalName">就诊医院：{{reserveParams.hospitalName}}</p>
          <p v-if="reserveParams.hospitalDept">就诊科室：{{reserveParams.hospitalDept}}</p>
          <p v-show="reserveParams.mediTime">就诊时间：{{reserveParams.mediTime}}</p>
        </div>
      </div>
    </div>

    <div class="footer">
      <van-button type="primary" class="btn" @click="reserve">立即预约</van-button>
    </div>
  </div>
</template>
<script>
import atbStep from './components/step.vue'
import api from '@/api/cityHealth'
import { theme, userInfoDict } from '@/mixin'
export default {
  mixins: [theme, userInfoDict],
  name: '',
  components: { atbStep },
  data() {
    return {
      icon: "",
      rightName: "",
      reserveParams: {},
      stepList: [
        {
          name: '预约告知',
        },
        {
          name: '预约申请',
        },
        {
          name: '预约安排',
        },
        {
          name: '预约完成',
        },
      ],
      stepIndex: '2', // 步进器索引值
    }
  },
  computed: {},
  watch: {},
  created() {
    this.reserveParams = JSON.parse(this.$route.query.reserveParams) || {}
    this.queryRights()
  },
  mounted() {},
  methods: {
    // 查询权益
    queryRights() {
      api.pbmCardRights({ cardNo: this.reserveParams.cardNo }, { noLoading: true }).then((res) => {
        var obj = res.result.filter((item) => item.rightCode == this.reserveParams.rightCode)[0]
        this.icon = obj.icon
        this.rightName = obj.rightName
      })
    },
    reserve() {
      if(this.reserveParams.birthday.split(" ").length == '1') {
        this.reserveParams.birthday = this.reserveParams.birthday + ' 00:00:00'
      }
      if(this.reserveParams.mediTime && this.reserveParams.mediTime.split(" ").length == '1') {
        this.reserveParams.mediTime = this.reserveParams.mediTime + ' 00:00:00'
      }
      this.reserveParams.idCardFront = this.reserveParams.idCardFront[0]
      this.reserveParams.idCardBack = this.reserveParams.idCardBack[0]
      this.reserveParams.height = Number(this.reserveParams.height)
      this.reserveParams.weight = Number(this.reserveParams.weight)
      api.reserve(this.reserveParams).then(() => {
        this.$router.push('/health-atb2023-reserveSuccess')
      })
    },
  },
}
</script>
<style lang="less" scoped>
.arrangement {
  height: 100vh;
  background: url('../../assets/images/common/com-bg.png') right top/1.36rem 1.4rem no-repeat , linear-gradient(360deg, #FFFFFF 0%, #FFF5F5 100%);
  overflow: auto;
  .reserve_box {
    width: 3.43rem;
    background: #fff;
    border-radius: .12rem;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    margin: 0 auto .12rem;
    padding: .16rem 0 0;
    .title {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.24rem;
      display: flex;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: #EB2A5C;
          border-radius: 0 0.02rem 0.02rem 0;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    .info {
      display: flex;
      align-items: flex-start;
      padding: .16rem;
      img {
        width: .6rem;
        height: .6rem;
      }
      p {
        margin-bottom: .12rem;
        color: #666666;
        line-height: .2rem
      }
    }
  }
  .footer {
    width: 100%;
    height: 0.8rem;
    border-top: 1px solid #f4f4f4;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
      border: none;
    }
  }
}
</style>