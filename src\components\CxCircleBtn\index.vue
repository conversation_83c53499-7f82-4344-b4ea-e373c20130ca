<template>
  <div
    :class="['circle-btn', 'default-bg']"
    :style="{color, background}"
  >
    <span>{{text1}}</span>
    <span>{{text2}}</span>
  </div>
</template>

<script>
export default {
  name: 'CircleBtn',
  props: {
    text: {
      type: String,
      default: '默认按钮'
    },
    color: {
      type: String,
      default: '#fff'
    },
    // 背景色
    background: {
      type: String,
      default: '#fe3132'
    },
  },
  data() {
    return {
      // 把内容拆成两行显示
      text1: '',
      text2: '',
    }
  },
  watch: {
    text: {
      immediate: true,
      handler(val) {
        this.text1 = val.slice(0, 2)
        this.text2 = val.slice(2)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.circle-btn {
  box-shadow: 0px 1px 5px 0px rgba(33, 33, 33, 0.3);
  width: 0.5rem;
  height: 0.5rem;
  overflow: hidden;
  border-radius: 50%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 0.16rem;
  font-size: 0.13rem;
  font-weight: 500;
  color: #fff;
  &.default-bg {
    &::before {
      content: '';
      display: block;
      width: 0.4rem;
      height: 0.4rem;
      border-radius: 50%;
      position: absolute;
      top: -0.07rem;
      left: -0.07rem;
      background: rgba(255, 255, 255, 0.19);
    }
    &::after {
      content: '';
      display: block;
      width: 0.25rem;
      height: 0.25rem;
      border-radius: 50%;
      position: absolute;
      bottom: 0;
      right: 0;
      background: rgba(255, 255, 255, 0.13);
    }
  }
}
</style>