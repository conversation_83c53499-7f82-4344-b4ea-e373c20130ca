<template>
  <div class="reserveSuccess">
    <img src="~images/atb/success.png" class="top_img">
    <p class="title">修改成功</p>
    <p class="content">您的修改信息已提交， <br>我们会尽快联系您，请您耐心等待！</p>
    <van-button class="btn" to="/health-orders">查看预约信息</van-button>
    <van-button class="other_btn">线上测评，了解癌症风险</van-button>
  </div>
</template>
<script>
export default {
  name: '',
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    to(path) {
      this.$router.push(path)
    }
  },
}
</script>
<style lang="less" scoped>
.reserveSuccess {
  .top_img {
    width: 1.2rem;
    margin: .8rem 1.27rem .16rem
  }
  .title {
    font-size: .2rem;
    line-height: .28rem;
    font-weight: 500;
    text-align: center;
  }
  .content {
    color: #666666;
    text-align: center;
    line-height: .22rem;
    margin-top: .4rem
  }
  .btn {
    width: 2.95rem;
    height: .48rem;
    border-radius: .24rem;
    margin: 0.4rem .4rem 0;
    font-size: .18rem;
    font-weight: 500;
    background: #F34168;
    border: none;
    color: #fff
  }
  .other_btn {
    width: 2.95rem;
    height: .48rem;
    border-radius: .24rem;
    margin: 0.16rem .4rem 0;
    font-size: .18rem;
    font-weight: 500;
    background: #fff;
    border: 1px solid #F34168;
    color: #F34168
  }
}
</style>