<template>
  <div class="activation">
    <img src="~images/bs2024/banner.png" width="100%" />
    <div class="form_block">
      <div class="active_block">
        <div class="title">
          <div class="tit-con">
            <i></i>
            <span>绑定权益人</span>
          </div>
        </div>
        <p class="content">*请输入百惠保被保人信息，其他用户暂不可使用该服务</p>
        <van-form ref="activeInfo">
          <van-field
            v-model="params.bindName"
            label="姓名"
            placeholder="请输入姓名"
            clearable
            :rules="validateRules.nameRules"
          />
          <cx-field-select
            label="证件类型"
            placeholder="请选择证件类型"
            optionsTitle="请选择证件类型"
            v-model="params.bindIdType"
            :options="idTypeList"
            :rules="validateRules.idTypeRules"
            color="linear-gradient(90deg, #F65D5E 0%, #DD1924 100%)"
            svgColor="#DD0000"
            style="border-bottom: 1px solid rgba(235, 237, 240, 0.5)"
          />
          <van-field
            v-model="params.bindIdNum"
            label="证件号码"
            placeholder="请输入证件号码"
            clearable
            :rules="idNumRules"
          />
        </van-form>
      </div>
    </div>

    <!-- 激活按钮 -->
    <div class="footer">
      <div class="module">
        <read-agreement :confirmRead.sync="confirmRead" :agreementQuery="agreeUrlQuery" :color="textColor"/>
      </div>
      <van-button :disabled="!completed" class="btn" @click="agreeCheck()">激活权益</van-button>
    </div>

    <!-- 协议弹窗 -->
    <van-popup v-model="agreementShow" class="dialog" position="bottom" closeable>
      <p class="dialog_title">服务协议及隐私保护</p>
      <div class="dialog_agreement">
        <span>为了更好的保护您的合法权益，请您阅读并同意以下协议</span>
        <zcx-agreement-box :isInline="true" :queryParams="agreeUrlQuery" :color="textColor"/>
      </div>
      <div class="btn_list">
        <van-button class="unAgree" @click="agreementShow = false">不同意</van-button>
        <van-button class="agree" @click="wbCheck = true">同意</van-button>
      </div>
    </van-popup>

    <!-- 微保校验 -->
    <van-popup v-model="wbCheck" class="dialog" position="bottom" closeable>
      <p class="dialog_title_wb"><img src="~images/yc2024/wb.png" >微保申请</p>
      <p class="dialog_sub_title_wb">获取以下权限</p>
      <p class="dialog_sub_label_wb">用于为您提供百惠保参保查询等服务</p>
      <p class="dialog_sub_notice_wb">-获取您的姓名、证件类型、证件号</p>
      <div class="dialog_agreement_wb">
        <span>我们将严格按照 <zcx-agreement-box :isInline="true" :queryParams="wbAgreeUrlQuery" :color="textColor"/>处理您的个人信息</span>
      </div>
      <div class="btn_list">
        <van-button class="agree_wb" @click="active()">确认</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { theme } from '@/mixin'
import { idTypeList } from './js/dictCode'
import { pipFormat } from '@/utils/tools'
import api from '@/api/cityHealth'
import api_user from '@/api/user'
import readAgreement from '@/pages/components/readAgreement'
import ZcxAgreementBox from '@/components/ZcxAgreementBox'
import validateRules from '@/utils/fieldValidateRules'
export default {
  mixins: [theme],
  components: { readAgreement, ZcxAgreementBox },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"268",keys:"sysm"},
      wbAgreeUrlQuery:{cardTypeCode:"268",keys:"ysxy"},
      textColor:"#DD0000",
      params: {
        cardTypeCode: '268',
        bindName: '',
        bindIdType: '1',
        bindIdNum: '',
      },
      confirmRead: false, // 阅读协议
      agreementShow: false, // 协议弹窗
      wbCheck: false, // 微保校验
      idTypeList, // 证件类型
      validateRules, // 校验规则
    }
  },
  computed: {
    completed() {
      return this.params.bindName && this.params.bindIdNum
    },
    // 证件号校验规则
    idNumRules() {
      const idType = this.params.bindIdType.toString()
      if (idType === '1') {
        // 身份证校验规则
        return validateRules.idNumRules
      } else {
        // 港澳居民来往内地通行证
        return validateRules.otherIdNumRules
      }
    },
  },
  watch: {
    'params.bindName': function (val) {
      this.params.bindName = pipFormat(val)
    },
    'params.bindIdNum': function (val) {
      this.params.bindIdNum = pipFormat(val)
    },
  },
  created() {
    this.registered()
  },
  mounted() {},
  methods: {
    // 校验阅读协议
    agreeCheck() {
      this.$refs.activeInfo
        .validate()
        .then(() => {
          if (!this.confirmRead) {
            this.agreementShow = true
            return
          }
          this.wbCheck = true
          // this.active()
        })
        .catch((err) => {
          console.info('校验失败,具体内容如下：')
          console.table(err)
        })
    },
    // 激活
    active() {
      var arr = []
      arr[0] = this.params
      api.activeBs2024(arr, { codeAllPass: true }).then((res) => {
        if (res.code === '200000') {
          if (this.$route.query.toFrom == 'back') {
            this.$router.go(-1)
          } else {
            this.$toast('激活成功')
            setTimeout(() => {
              this.$router.push('/health-bs2024-origin')
            }, 1500)
          }
        } else {
          this.agreementShow = false
          this.$toast(res.message)
        }
      })
    },
    // 查询登录信息
    registered() {
      api_user
        .registered({
          jumpSource: '',
          routing: '',
          extraParams: '',
        })
        .then((res) => {
          if (!res.result.registed) {
            this.$router.push({
              path: '/login',
              query: {
                returnUrl: encodeURIComponent(location.href),
              },
            })
          }
        })
    },
  },
}
</script>
<style lang="less" scoped>
.activation {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .form_block {
    flex: 1;
    padding: 0.12rem 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 0.24rem 0.24rem 0 0;
    margin-top: -0.2rem;
    .active_block {
      width: 3.51rem;
      background: #ffffff;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
      border-radius: 0.16rem;
      border: 1px solid #ffffff;
      margin: 0 auto;
      padding-top: 0.2rem;
      .title {
        font-size: 0.18rem;
        font-weight: 500;
        line-height: 0.22rem;
        display: flex;
        .tit-con {
          flex: 1;
          display: flex;
          align-items: center;
          i {
            display: inline-block;
            vertical-align: middle;
            margin-right: 0.12rem;
            width: 0.04rem;
            height: 0.2rem;
            background: #DD0000;
            border-radius: 0 0.02rem 0.02rem 0;
          }
          span {
            vertical-align: middle;
            line-height: 0.26rem;
          }
        }
      }
      .content {
        color: #DD0000;
        font-size: 0.12rem;
        padding: 0.12rem 0.16rem 0;
        line-height: 0.17rem;
      }
    }
  }
  .footer {
    background: #fff;
    width: 100%;
    .module {
      display: flex;
      background: #fff;
      padding: 0.12rem 0.16rem;
      box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
    }
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      border-radius: 0.24rem;
      border: none;
      margin: 0 0.16rem 0.16rem;
      color: #fff;
      font-size: 0.18rem;
      line-height: 0.24rem;
      font-weight: 500;
    }
  }

  .dialog {
    width: 100%;
    padding-top: 0.24rem;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 0.24rem 0.24rem 0px 0px;
    .dialog_title {
      font-size: 0.18rem;
      font-weight: bold;
      line-height: 0.28rem;
      color: #222222;
      text-align: center;
    }
    .dialog_agreement {
      padding: 0.24rem 0.24rem 0.48rem;
    }
    .dialog_title_wb {
      font-size: .16rem;
      font-weight: 400;
      color: #666666;
      line-height: .24rem;
      display: flex;
      align-items: center;
      margin-top:-.07rem;
      img {
        width: .42rem;
        margin-right: .11rem;
        margin-left: .23rem;
      }
    }
    .dialog_sub_title_wb {
      font-size: .18rem;
      font-weight: 500;
      color: #222222;
      line-height: 24px;
      padding: .15rem .24rem 0
    }
    .dialog_sub_label_wb {
      padding: .08rem .24rem;
      font-size: .14rem;
      font-weight: 400;
      color: #222222;
      line-height: .2rem;
    }
    .dialog_sub_notice_wb {
      padding: .12rem;
      margin: .12rem .24rem;
      background: #FAFAFA;
      border-radius: .04rem;
      font-size: .14rem;
      font-weight: 400;
      color: #666666;
      line-height: .24rem;
    }
    .dialog_agreement_wb {
      padding: .12rem .24rem;
      font-size: .12rem;
      font-weight: 400;
      color: #222222;
      line-height: .18rem;
    }
    .btn_list {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 1px solid #f4f4f4;
      padding: 0.16rem 0;
      .unAgree {
        width: 1.2rem;
        height: 0.48rem;
        background: #ffffff;
        border-radius: 0.24rem;
        padding: 0;
        background: #fff;
        font-size: 0.18rem;
        line-height: 0.24rem;
        color: #DD0000;
        border: 1px solid;
        border-color: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%) 1 1;
      }
      .agree {
        width: 2.11rem;
        height: 0.48rem;
        color: #fff;
        border-radius: 0.24rem;
        padding: 0;
        font-size: 0.18rem;
        line-height: 0.24rem;
        margin-left: 0.12rem;
        background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      }
      .agree_wb {
        width: 3.43rem;
        height: 0.48rem;
        color: #fff;
        border-radius: 0.24rem;
        padding: 0;
        font-size: 0.18rem;
        line-height: 0.24rem;
        margin-left: 0.12rem;
        background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      }
    }
  }
  /deep/.van-form {
    padding: 0 0.16rem;
    .van-field {
      padding: 0.16rem 0;
    }
    .van-field .van-field__label {
      width: 0.88rem;
      margin-right: 0;
      color: #666666;
    }
    .van-cell {
      align-items: center;
    }
    .van-cell::after {
      left: 0;
      right: 0;
    }
    .van-field__button {
      padding: 0.06rem 0.1rem;
      background: #DD0000;
      border-radius: 0.18rem;
    }
    .van-field .van-cell__right-icon {
      bottom: 16px;
      right: 0;
    }
  }
}
</style>
