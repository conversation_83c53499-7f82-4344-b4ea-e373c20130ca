<template>
  <div align="center">
    <van-field
      v-bind="$attrs"
      v-on="$listeners"
      v-model="textValue"
      :disabled="this.$attrs.disabled"
      :clickable="!this.$attrs.disabled"
      :is-link="!this.$attrs.disabled"
      readonly
      @click="showOptions"
    />

    <!-- 选择器 -->
    <van-action-sheet v-bind="actionSheetProps" v-model="isShowActionSheet" :description="optionsTitle" :closeable="true">
      <div class="content" >
        <van-radio-group v-model="type">
          <van-cell-group :border="false">
            <div v-for="(item, index) in options" :key="index">
              <van-cell clickable @click="choosePayType(item)">
                <template #title>
                  <span>{{item.name}}</span>
                </template>
                <template #right-icon>
                  <van-radio :name="item.value" :checked-color="theme.primaryColor">
                    <template #icon="props">
                      <img class="img-icon" :src="props.checked ? require(`../../../assets/images/hxb/cicon-selected.png`) : require(`../../../assets/images/icon/cicon-unselected.png`)" />
                    </template>
                  </van-radio>
                </template>
              </van-cell>
            </div>
          </van-cell-group>
        </van-radio-group>
      </div>

      <div class="footer">
        <div class="btn" style="background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);" @click.stop="handleSelectOption()">确定</div>
      </div>
    </van-action-sheet>
    
  </div>
</template>

<script>
import { theme } from '@/mixin'
export default {
  mixins: [theme],
  name: 'CxFieldSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // actionSheet组件的actions（面板选项列表）
    options: {
      type: Array,
      default() {
        return []
      }
    },
    // 数据选项对应的标签和值的key
    keyMap: {
      type: Array,
      default() {
        return ['name', 'value']
      }
    },
    // 对应actionSheet的description属性
    optionsTitle: {
      type: String,
      default: ''
    },
    // actionSheet组件的所有属性
    actionSheetProps: {
      type: Object,
      default() {
        return {}
      }
    },
  },
  data() {
    return {
      type: "",
      isShowActionSheet: false,
    }
  },
  mounted() {},
  computed: {
    // 当前选择项的lable(中文描述)
    textValue() {
      const currentOption = this.options.find(item => item[this.keyMap[1]] == this.value)
      return currentOption ? currentOption[this.keyMap[0]] : ''
    },
    // actionSheet组件的actions属性
    actions() {
      return this.options.map(option => {
        return {
          ...option,
          name: option[this.keyMap[0]]
        }
      })
    }
  },
  watch: {
    'value': function(val) {
      this.type = val
    }
  },
  methods: {
    // 显示选项弹窗
    showOptions() {
      if (!this.$attrs.disabled) {
        this.isShowActionSheet = true
      }
    },
    // 选择某一项时，返回该项的value（值），并隐藏actionSheet组件Z
    handleSelectOption() {
      if(this.type || this.type === 0) {
        this.$emit('change', this.type)
        this.isShowActionSheet = false
      } else {
        this.$toast('请选择')
      }
      
    },
    choosePayType(item) {
      this.type = item[this.keyMap[1]]
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.van-action-sheet__description {
  font-size: .2rem;
  color: #222222
}
/deep/.van-field .van-cell__right-icon {
  bottom : .13rem;
  right: 0
}
/deep/.van-cell::after {
  border-bottom: none
}
.content {
  padding-bottom: .8rem;
  .img-icon {
    width: .16rem
  }
}

.footer {
  width: 100%;
  height: .8rem;
  border-top: 1px solid #F4F4F4;
  background: #fff;
  position: absolute;
  bottom: 0;
  left:0;
  z-index:1000;
  display: flex;
  align-items: center;
  justify-content: center;
  .btn {
    width: 3.43rem;
    height: .48rem;
    line-height: .48rem;
    border-radius: .24rem;
    color: #fff;
    font-size: .18rem
  }
}

</style>