<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <!-- <meta http-equiv="pragram" content="no-cache"> -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
  <link rel="icon" href="https://static.mediext.com/cx_icon.ico" type="image/x-icon" />
  <title></title>
  <link rel="stylesheet" href="//medi-static.oss-cn-shanghai.aliyuncs.com/index.css" />
  <!-- <script src="//medi-static.oss-cn-shanghai.aliyuncs.com/vue.min.js"></script>
    <script src="//medi-static.oss-cn-shanghai.aliyuncs.com/vant.min.js"></script>
    <script src="//medi-static.oss-cn-shanghai.aliyuncs.com/vue-router.min.js"></script>  
    <script src="//medi-static.oss-cn-shanghai.aliyuncs.com/axios.min.js"></script>
     <script src="//medi-static.oss-cn-shanghai.aliyuncs.com/vuex.min.js"></script> -->
  <!-- <script src="//medi-static.oss-cn-shanghai.aliyuncs.com/jweixin-1.6.0.js"></script> -->
  <!-- 引入百度统计 -->
  <script src="./html/BDTJ.js"></script>
  <!-- 平安 -->
  <script src="https://statics-stg1.pingan.com.cn/m/release/lib/hflJsBridge.js"></script>
</head>


<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
      properly without JavaScript enabled. Please enable it to
      continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
  <!-- <script src="https://medi-static.oss-cn-shanghai.aliyuncs.com/vue.js"></script> -->
  <!-- <script src="https://medi-static.oss-cn-shanghai.aliyuncs.com/vue.min.js"></script> -->
  <!-- <script src="https://medi-static.oss-cn-shanghai.aliyuncs.com/axios.min.js"></script>
    <script src="https://medi-static.oss-cn-shanghai.aliyuncs.com/vuex.min.js"></script>
    <script src="https://medi-static.oss-cn-shanghai.aliyuncs.com/vue-router.min.js"></script> -->
  <script>
    // rem定义
    /*720代表设计师给的设计稿的宽度，你的设计稿是多少，就写多少;100代表换算比例，这里写100是
            为了以后好算,比如，你测量的一个宽度是100px,就可以写为1rem,以及1px=0.01rem等等*/
    getRem(375, 100);

    window.onresize = function () {
      getRem(375, 100);
    };

    function getRem(pwidth, prem) {
      var html = document.getElementsByTagName("html")[0];
      var oWidth =
        document.documentElement.clientWidth || document.body.clientWidth;
      html.style.fontSize = (oWidth / pwidth) * prem + "px";
    }

    // 安卓机中，默认字体大小不让用户修改
    ;
    (function () {
      if (typeof WeixinJSBridge == 'object' && typeof WeixinJSBridge.invoke == 'function') {
        handleFontSize()
      } else {
        if (document.addEventListener) {
          document.addEventListener('WeixinJSBridgeReady', handleFontSize, false)
        } else if (document.attachEvent) {
          document.attachEvent('WeixinJSBridgeReady', handleFontSize)
          document.attachEvent('onWeixinJSBridgeReady', handleFontSize)
        }
      }

      function handleFontSize() {
        // 设置网页字体为默认大小
        WeixinJSBridge.invoke('setFontSizeCallback', {
          fontSize: 0,
        })
        // 重写设置网页字体大小的事件
        WeixinJSBridge.on('menu:setfont', function () {
          WeixinJSBridge.invoke('setFontSizeCallback', {
            fontSize: 0,
          })
        })
      }
    })()
  </script>
</body>

</html>