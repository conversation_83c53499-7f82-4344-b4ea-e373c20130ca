<template>
  <div class="orders">
    <!--  -->
    <van-tabs
      v-model="status"
      @click="changeTabs"
      color="#EB2A5C"
      title-active-color="#EB2A5C"
      title-inactive-color="#666666"
      line-width="0"
      line-height="0"
      class="tabs"
    >
      <van-tab
        v-for="item in tabList"
        :title="item.name"
        :name="item.value"
        :key="item.value"
      ></van-tab>
    </van-tabs>

    <div class="com-block order-block">
      <van-empty
        v-show="orderList.length === 0" 
        :image="require('../../assets/images/common/empty.png')"
        description="预约列表为空" 
      />

      <van-list
        v-show="orderList.length !== 0"
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div class="orderList" ref="orderList">
          <div class="order" v-for="(item, index) in orderList" :key="index">
            <p class="status" v-status="item.status">{{dictObj[item.status]}}</p>
            <img :src="item.rightIcon">
            <div style="display: inline-block;vertical-align: top;max-width: 1.62rem;">
              <p class="rightName">{{item.rightName}}</p>
              <p class="createTime">申请时间：{{item.createTime.split(' ')[0]}}</p>
              <p class="bindName">就诊人：{{item.bindName}}</p>
            </div>
            <van-button type="primary" class="detail_btn" @click="toDetail(item.id, item.status)">预约详情</van-button>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import api_global from '@/api/global'
import { theme } from '@/mixin'
import { List } from 'vant';
import { getValueByKeyFromURL } from '@/utils/tools'
export default {
  mixins: [theme],
  name: '',
  components: { "van-list":List },
  data() {
    return {
      status: "", // 预约状态
      orderList: [], // 预约订单列表
      page: 1,
      loading: false, // 分页加载
      finished: false, // 分页到底
      arrLength: 0, // 当前订单接口列表长度
      tabList: [{
        name: "全部",
        value: ""
      },{
        name: "申请中",
        value: "UNPROCESSED,TOADD"
      },{
        name: "服务中",
        value: "PROCESSED,CONFIRM"
      }, {
        name: "已完成",
        value: "ADMINCANCEL,USERCANCEL,REJ,FINISHED,REFUNDED"
      }],
      dictObj: {}, // 字典对象
      filterTypeCode: getValueByKeyFromURL(location.href, 'filterTypeCode') || ""
    }
  },
  directives: {
    // 订单状态颜色指令
    status: function(el, binding) {
      let color = ''
      let background = ''
      switch (binding.value) {
        case 'UNPROCESSED':
        case 'WAITPAY':
          color = '#EB2A5C';
          background = '#FFE5E5'
          break
        case 'PROCESSED':
        case 'CONFIRM':
          color = '#32B9BC';
          background = '#E4FEFF'
          break
        case 'TOADD':
          color = '#FF7311';
          background = '#FFF6F0'
          break
        case 'FINISHED':
          color = '#60B96C';
          background = '#E8FFEB'
          break;
        default:
          color = '#BBBBBB'
          background = '#FAFAFA'
      }
      el.style.color = color
      el.style.background = background
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.queryDictObject()
    this.queryOrders()
  },
  methods: {
    queryDictObject() {
      api_global.queryDictObject('PBM_CARD_BOOKING_STATUS').then((res) => {
        this.dictObj = res.result
      })
    },
    // 点击切换tab时
    changeTabs(name) {
      this.status = name;
      this.page = 1;
      this.orderList = [];
      this.arrLength = 0
      this.queryOrders()
    },
    queryOrders() {
      api.queryOrders({
        page: this.page,
        pageSize: 10,
        status: this.status,
        cardTypeCode: '196'
      }).then((res) => {
        this.orderList = this.orderList.concat(res.result.results)
        this.arrLength = res.result.results.length
      })
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      setTimeout(() => {
        this.page = this.page + 1
        this.queryOrders()
        // 加载状态结束
        this.loading = false;
        // 数据全部加载完成
        if (this.arrLength < 10) {
          this.finished = true;
        }
      }, 1000);
    },
    toDetail(id, status) {
      var completed = Number
      switch(status) {
        case 'UNPROCESSED':
        case 'TOADD':
        case 'ADMINCANCEL':
        case 'USERCANCEL':
        case 'WAITPAY':
          completed = 0
          break;
        case 'PROCESSED':
        case 'CONFIRM':
          completed = 1
          break;
        
        case 'REJ':
        case 'FINISHED':
          completed = 3
          break
      }
      this.$router.push({
        path: "/health-atb2023-orderDetail",
        query: {
          id: id,
          completed: completed
        }
      })
    }
  },
}
</script>
<style lang="less" scoped>
.orders {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../../assets/images/common/com-bg.png') right top/1.36rem 1.4rem no-repeat , linear-gradient(360deg, #FFFFFF 0%, #FFF5F5 100%);
  overflow: auto;
  .appT {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10000
  }
  /deep/.van-tabs__nav {
    background: transparent
  }
  /deep/.van-tabs__wrap--scrollable .van-tabs__nav--complete {
    padding-left: 0px;
    padding-right: 0px;
    .van-tab {
      padding: 0 0.2rem;
      &.van-tab--active {
        color: #EB2A5C;
      }
    }
  }
  .tabs {
    width: 3.75rem;
    height: .44rem;
    // position: fixed;
    left:0;
    top:0;
    z-index:100
  }
  /deep/.van-empty {
    margin-top: 1rem;
    .van-empty__image {
      width: 2.5rem;
      height: 1.8rem;
      display: flex;
    }
    .van-empty__description {
      margin-top: 0.24rem;
      font-size: .14rem;
      color: #999999;
    }
  }
  .order-block {
    margin-top: 0;
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    width: 100%
  }
  .orderList {
    padding: 0 0.16rem;
    .order {
      width: 3.27rem;
      padding: .15rem .08rem;
      background: #fff;
      border-radius: .16rem;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.06); 
      margin: 0 auto .12rem;
      position: relative;
      overflow: hidden;
      .status {
        background: #EAF3FF;
        padding: .06rem .16rem;
        border-radius: 0px 0px 0px 16px;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 100;
        font-size: .16rem;
        line-height: .22rem
      }
      img {
        width: .72rem;
        height: .72rem;
        display: inline-block;
        vertical-align: top;
        margin-right: .04rem
      }
      .rightName {
        line-height: .2rem;
        font-weight: 500;
        padding: .04rem 0 .12rem;
        max-width:1.86rem
      }
      .createTime {
        color: #666666;
        font-size: .12rem;
        padding-bottom: .12rem;
        line-height: .18rem
      }
      .bindName {
        color: #666666;
        line-height: .18rem
      }
      .detail_btn {
        width: .8rem;
        height: .32rem;
        border-radius: .16rem;
        background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
        box-shadow: 0px 2px 8px 0px rgba(221,0,0,0.2);
        border: none;
        position: absolute;
        bottom: 0.2rem;
        right: 0.16rem;
        padding: 0;
      }
    }
  }
}
</style>