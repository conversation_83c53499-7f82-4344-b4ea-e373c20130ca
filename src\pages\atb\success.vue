<template>
  <div class="reserveSuccess">
    <img src="~images/atb/success.png" class="top_img">
    <p class="title">预约申请提交成功</p>
    <p class="content">您的预约信息已提交， <br>我们会尽快联系您，请您耐心等待！</p>
    <van-button class="btn" to="/health-orders">查看预约信息</van-button>
    <div v-show="miniShow" class="other_btn" @click.capture="getPvUv">
      <wx-open-launch-weapp id="launch-btn" username="gh_1b5a634ea9da" path="pages/hospital/index?hospitalId=213" style="width:2.95rem">
        <script type="text/wxtag-template">
          <style>
            .btn {
              width: 100%;
              height: 48px;
              border-radius: 24px;
              margin: 0;
              background: #fff;
              border: 1px solid #F34168;
              color: #F34168;
              font-size: 18px;
              font-weight: 500;
            }
          </style>
          <button class="btn">线上测评，了解癌症风险</button>
        </script>
      </wx-open-launch-weapp>
    </div>
  </div>
</template>
<script>
import api_global from '@/api/global'
import wx from 'weixin-js-sdk'
export default {
  name: '',
  components: {},
  data() {
    return {
      miniShow: true
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.signature()
  },
  methods: {
    to(path) {
      this.$router.push(path)
    },
    getPvUv() {
      console.log('pvuv')
    },
    // 微信菜单
    signature() {
      const currentUrl = window.location.href.split('#')[0]
      api_global.signature({ url: currentUrl }).then((res) => {
        const configInfo = (res && res.result) || {}
        wx.config({
          debug: false,
          appId: configInfo.appId,
          timestamp: configInfo.timestamp.toString(),
          nonceStr: configInfo.nonceStr,
          signature: configInfo.signature,
          jsApiList: ['wx-open-launch-weapp'],
          openTagList: ['wx-open-launch-weapp'],
        })

        wx.ready((res) => {
          console.log('ready:', res)
        })
        wx.error((res) => {
          console.log('jssdk error')
          console.log(res)
        })
      })
    },
  },
}
</script>
<style lang="less" scoped>
.reserveSuccess {
  .top_img {
    width: 1.2rem;
    margin: .8rem 1.27rem .16rem
  }
  .title {
    font-size: .2rem;
    line-height: .28rem;
    font-weight: 500;
    text-align: center;
  }
  .content {
    color: #666666;
    text-align: center;
    line-height: .22rem;
    margin-top: .4rem
  }
  .btn {
    width: 2.95rem;
    height: .48rem;
    border-radius: .24rem;
    margin: 0.4rem .4rem 0;
    font-size: .18rem;
    font-weight: 500;
    background: #F34168;
    border: none;
    color: #fff
  }
  .other_btn {
    width: 2.95rem;
    margin: 0.16rem .4rem 0;
  }
  .dialog {
    width: 3.11rem;
    background: #ffffff;
    border-radius: .16rem;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    .title {
      margin: .24rem auto .16rem;
      text-align: center;
      font-size: .2rem;
      font-weight: 500;
      line-height: .28rem
    }
    .text {
      font-size: .16rem;
      line-height: .24rem;
      padding: 0 .24rem .24rem;
      text-align: center;
    }
  }
}
</style>