<template>
  <div class="atb-origin">
    <div class="avatar">
      <img src="~images/atb/avatar.png" alt="" class="avatar-img" />
      <div>
        <p class="phoneNo">{{currentCardInfo.bindPhoneNo}}</p>
        <p class="toOrders" @click="toOrders">我的预约<img src="~images/atb/avatar-arrow.png" alt="" /></p>
      </div>
    </div>
    <div class="card_box">
      <div class="holder_box">
        <p class="title">权益人信息</p>
        <div class="holderInfo">
          <p v-show="currentCardInfo.bindName" class="name">{{ currentCardInfo.bindName }}</p>
          <p v-show="currentCardInfo.bindIdNum" style="display: flex; align-items: center">
            <img src="~images/icon/idNum.png" alt="" />{{ currentCardInfo.bindIdNum }}
          </p>
        </div>
        <!-- 切换权益人按钮 -->
        <van-button
          type="text"
          :icon="require('../../assets/images/atb/icon-exchange.png')"
          class="exchange-btn"
          @click="holderShow = true"
          >切换权益人</van-button
        >
      </div>

      <!-- 权益展示 -->
      <div class="right_box">
        <p class="title">权益展示</p>
        <van-empty
          v-show="rightsList.length === 0" 
          :image="require('../../assets/images/atb/empty.png')"
          :description="'您暂无此权益\n请激活权益后再次尝试'" 
        />
        <div v-if="rightsList.length > 0">
          <div class="rightInfo" v-for="(item, index) in rightsList" :key="index">
            <img v-if="item.icon" :src="item.icon" class="rightImg">
            <img v-else :src="require(`../../assets/images/atb/swipe_${item.rightCode}.png`)" class="rightImg">
            <div>
              <p class="rightName">{{ item.rightName }}</p>
              <p class="content">{{ item.rightTips }}</p>
              <p class="endTime">有效期至{{ item.endTime.split(' ')[0] }}</p>
            </div>
            <p class="status">已激活</p>
            <van-button class="rightBtn" @click="toReserve(item)">立即查看</van-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 权益人选择器 -->
    <van-action-sheet
      v-model="holderShow"
      description="权益人"
      :closeable="true"
      :close-icon="require('../../assets/images/icon/close.png')"
    >
      <div class="content" :style="{ 'max-height': height + 'px' }">
        <van-radio-group v-model="actionCardInfo">
          <van-cell-group :border="false">
            <div v-for="(item, index) in cardList" :key="index">
              <van-cell clickable>
                <template #title>
                  <span>{{ item.bindName }}</span>
                </template>
                <template #label>
                  <img src="~images/icon/idNum.png" class="img-icon" />
                  <p>{{ item.bindIdNum }}</p>
                </template>
                <template #right-icon>
                  <van-radio :name="item" checked-color="#F34168" @click="confirmAction"> </van-radio>
                </template>
              </van-cell>
            </div>
          </van-cell-group>
        </van-radio-group>
      </div>

      <div class="footer">
        <van-button type="primary" class="btn" to="/health-atb-activation">新增</van-button>
      </div>
    </van-action-sheet>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import { maskString } from '@/utils/tools'
export default {
  name: '',
  components: {},
  data() {
    return {
      cardList: [], // 权益卡列表
      currentCardInfo: {}, // 当前权益人卡信息
      rightsList: [], // 权益列表
      holderShow: false, // 选择权益人弹窗
      actionCardInfo: '',
      height: document.documentElement.clientHeight * 0.7,
    }
  },
  computed: {},
  watch: {
    
  },
  created() {},
  mounted() {
    this.queryCardList()
  },
  methods: {
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList({cartTypeCode: "103"}).then((res) => {
        res.result.forEach((ele) => {
          ele.bindIdNum = maskString(ele.bindIdNum, 2, 4)
          ele.bindPhoneNo = maskString(ele.bindPhoneNo, 3, 4)
        })
        this.cardList = res.result
        this.currentCardInfo = this.actionCardInfo = this.cardList.length > 0 && this.cardList[0]
        if(this.cardList.length > 0) {
          this.queryRights()
        }
      })
    },
    // 确认选择当前权益人
    confirmAction() {
      this.currentCardInfo = this.actionCardInfo
      this.queryRights()
      this.holderShow = false
    },
    queryRights() {
      api.pbmCardRights({ cardNo: this.currentCardInfo.cardNo }).then((res) => {
        this.rightsList = res.result
      })
    },
    // 查看权益
    toReserve(event) {
      var urlObj = event.url && JSON.parse(event.url) || ""
      if(urlObj && urlObj.type == 'url') {
        api.getPvUv({ eventOwner: '103', eventCode: event.rightCode, eventType: 'RIGHTS'}).then(() => {
          location.href = urlObj.path
        })
      } else {
        this.$router.push({
          path: event.rightCode == 'PR011' ? "/health-atb-introTRT" : "/health-atb-introduction",
          query: {
            cardNo: event.cardNo
          }
        })
      }
    },
    toOrders() {
      this.$router.push('/health-orders')
    },
  },
}
</script>
<style lang="less" scoped>
.atb-origin {
  background: linear-gradient(90deg, #fac18f 0%, transparent),
    linear-gradient(90deg, transparent, #e41857 100%);
  background-image: url('../../assets/images/atb/origin-bg.png');
  background-repeat: no-repeat;
  background-size: 100%;
  height: 100vh;
  overflow: auto;
  .avatar {
    padding: 0.3rem 0.14rem 0;
    color: #f34168;
    display: flex;
    align-items: center;
    .avatar-img {
      width: 0.6rem;
      margin-right: 0.14rem;
    }
    .phoneNo {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.24rem;
      margin-bottom: 0.08rem;
    }
    .toOrders {
      line-height: 0.2rem;
      display: flex;
      align-items: center;
      img {
        width: 0.06rem;
        margin-left: 0.08rem;
      }
    }
  }
  .card_box {
    margin: 1.44rem auto 0.16rem;
    width: 3.43rem;
    background: #fafafa;
    border-radius: 0.12rem;
    overflow: hidden;
    .holder_box {
      padding: 0.16rem;
      position: relative;
      .title {
        font-size: 0.16rem;
        line-height: 0.22rem;
        font-weight: 500;
      }
      .holderInfo {
        color: #666666;
        font-size: 0.12rem;
        line-height: 0.16rem;
        display: flex;
        align-items: center;
        margin-top: 0.12rem;
        .name {
          padding-right: 0.08rem;
          border-right: 1px solid #cccccc;
          line-height: 0.14rem;
          margin-right: 0.07rem;
        }
        img {
          width: 0.16rem;
          height: 0.16rem;
          margin-right: 0.04rem;
        }
      }
      .exchange-btn {
        background: transparent;
        border: none;
        padding: 0;
        color: #f34168;
        font-size: 0.12rem;
        line-height: 0.18rem;
        height: auto;
        position: absolute;
        right: 0.16rem;
        top: 0.18rem;
      }
      .van-button__icon {
        font-size: 0.18rem;
      }
    }
    .right_box {
      border-radius: .12rem;
      padding: .16rem .16rem 0.04rem;
      background: #fff;
      .title {
        font-size: .16rem;
        line-height: .22rem;
        font-weight: 500;
      }
      /deep/.van-empty {
        .van-empty__image {
          width: 1.67rem;
          height: 1.21rem;
          display: flex;
        }
        .van-empty__description {
          margin-top: 0.24rem;
          font-size: .14rem;
          color: #999999;
          white-space: pre-wrap;
          text-align: center;
        }
      }
      .rightInfo {
        width: 3.11rem;
        padding: .12rem 0 .16rem;
        margin: 0 auto;
        border-bottom: 1px solid #F4F4F4;
        position: relative;
        display: flex;
        align-items: center;
        .rightImg {
          width: .72rem;
          margin-right: .04rem
        }
        .rightName {
          font-weight: 500;
          line-height: .2rem
        }
        .content {
          font-size: .12rem;
          color: #F34168;
          line-height: .18rem;
          margin: .08rem auto
        }
        .endTime {
          font-size: .12rem;
          color: #666666;
          line-height: .18rem;
        }
        .status {
          width: .44rem;
          height: .2rem;
          line-height: .2rem;
          background: rgba(243, 65, 104, 0.05);
          border-radius: .04rem;
          font-size: .12rem;
          color: #F34168;
          text-align: center;
          position: absolute;
          right: 0;
          top: 0.2rem
        }
        .rightBtn {
          width: .8rem;
          height: .32rem;
          background: #F34168;
          box-shadow: 0px 2px 8px 0px rgba(243, 65, 104, 0.2);
          border-radius: .16rem;
          padding: 0;
          border: none;
          color: #fff;
          font-weight: 500;
          position: absolute;
          bottom: .16rem;
          right: 0
        }
      }
      .rightInfo:last-child {
        border-bottom: none
      }
    }
  }
  /deep/.van-action-sheet__description {
    font-size: 0.2rem;
    line-height: 0.28rem;
    font-weight: 500;
    color: #222222;
    padding: 0.2rem 0;
  }
  /deep/.van-action-sheet__description::after {
    border-bottom: none;
  }
  .content {
    .van-cell-group {
      padding: 0 0.16rem;
      padding-bottom: 0.8rem;
    }
    .van-cell {
      background: #fafafa;
      border-radius: 0.12rem;
      overflow: hidden;
      margin-bottom: 0.12rem;
      padding: 0.12rem 0.16rem;
    }
    .van-cell__title,
    .van-cell__value {
      font-size: 0.14rem;
      font-weight: 500;
      color: #222222;
      line-height: 0.2rem;
    }
    .van-cell__label {
      font-size: 0.12rem;
      color: #666666;
      font-weight: 400;
      line-height: 0.16rem;
      display: flex;
      align-items: center;
      margin-top: 0.08rem;
    }
    .img-icon {
      width: 0.16rem;
      margin-right: 0.04rem;
    }
  }
  .footer {
    width: 100%;
    height: 0.8rem;
    border-top: 1px solid #f4f4f4;
    background: #fff;
    position: fixed;
    bottom: 0rem;
    left: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      background: #f34168;
      border: none
    }
  }
  .dialog {
    width: 3.11rem;
    background: #ffffff;
    border-radius: .16rem;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    .title {
      margin: .24rem auto .16rem;
      text-align: center;
      font-size: .2rem;
      font-weight: 500;
      line-height: .28rem
    }
    .text {
      font-size: .16rem;
      line-height: .24rem;
      padding: 0 .24rem;
      text-align: center;
    }
    .cancel {
      width: 2.63rem;
      height: .48rem;
      border-radius: .24rem;
      margin: .12rem .24rem .24rem;
      background: #fff;
      border: 1px solid #F34168;
      font-size: .16rem;
      color: #F34168
    }
  }
}
</style>