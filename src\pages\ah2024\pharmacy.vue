<template>
  <div class="pharmacy">
    <!-- 市列表--左 -->
    <div class="tab_left">
      <p v-for="(item, index) in cityList" :key="index" class="cityName" :class="{active: activeCity == index}" @click="activeCity = index;queryPharmacy()">
        {{ item.city }}
      </p>
    </div>
    <!-- 药房，区--右 -->
     <div class="tab_right">
      <!-- 区 -->
      <div class="district">
        <p v-for="(item, index) in cityList[activeCity].districtList" :key="index" class="districtName" :class="{activeDis: activeDistrict == index}" @click="activeDistrict = index;queryPharmacy()" :style="{'min-width': item.district.length == 4 ? '.56rem' : '.42rem'}">
          {{ item.district }}
        </p>
      </div>
      <div class="main_box">
        <div v-for="( item, index ) in pharmacyList" :key="index" class="phar_box">
          <div class="name">
            <svg-icon iconName="ah_phar"></svg-icon>
            <p style="flex: 1">{{ item.name }}</p>
          </div>
          <div class="address">
            <svg-icon iconName="address" color="#444444"></svg-icon>
            <p style="flex: 1">{{ item.address }}</p>
          </div>
        </div>
      </div>
     </div>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
export default {
  components: {},
  data() {
    return {
      rightCode: this.$route.query.rightCode || '',
      cityList: [],
      activeCity: 0,
      activeDistrict: 0,
      pharmacyList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    this.queryArea()
  },
  mounted() {},
  methods: {
    // 查询省市区
    queryArea() {
      api.queryArea({ rightCode: this.rightCode }).then((res) => {
        this.cityList = res.result[0].cityList
        this.queryPharmacy()
      })
    },
    // 查询药房
    queryPharmacy() {
      api.queryPharmacy({ 
        rightCode: this.rightCode, 
        city: '安徽省' + this.cityList[this.activeCity].city + this.cityList[this.activeCity].districtList[this.activeDistrict].district
      }).then((res) => {
        this.pharmacyList = res.result
      })
    }
  },
}
</script>
<style lang="less" scoped>
.pharmacy {
  height: 100vh;
  display: flex;
  width: 3.75rem;
  background: #fafafa;
  .tab_left {
    width: 0.88rem;
    height: 100vh;
    overflow-y: auto;
    border-right: 1px solid #f4f4f4;
    display: flex;
    flex-direction: column;
    .cityName {
      border-bottom: 1px solid #f4f4f4;
      font-size: 0.14rem;
      color: #444444;
      line-height: 0.2rem;
      padding: 0.12rem 0 0.12rem 0.16rem;
    }
    .active {
      background: #FD5710;
      color: #fff;
    }
    .cityName:last-of-type {
      border-bottom: none;
    }
  }
  .tab_right {
    flex: 1;
    overflow-x: hidden;
    height: 100vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .district {
      padding: .16rem;
      overflow-x: auto;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #F4F4F4;
      .districtName {
        padding: .04rem .08rem;
        min-width: .42rem;
        background: #F2F2F2;
        border-radius: .04rem;
        font-size: .14rem;
        color: #444444;
        line-height: .2rem;
        display: inline-block;
        vertical-align: middle;
        margin-right: .08rem;
      }
      .activeDis {
        background: #FD5710;
        color: #fff;
      }
      .districtName:last-of-type {
        margin-right: 0;
      }
    }
    .main_box {
      background: #fff;
      flex: 1;
      overflow-y: auto;
      .phar_box {
        border-bottom: 1px solid #F4F4F4;
        margin: 0 .16rem;
        padding: .16rem 0;
        .name {
          font-size: .14rem;
          color: #222222;
          line-height: .2rem;          
          display: flex;
          align-items: center;
        }
        .address {
          font-size: .12rem;
          color: #444444;
          line-height: .16rem;
          display: flex;
          align-items: flex-start;
          margin-top: .08rem;
        }
        .svg-icon {
          width: .16rem;
          height: .16rem;
          margin-right: .06rem;
        }
      }
      .phar_box:last-of-type {
        border-bottom: none;
      }
    }
  }
}
</style>
