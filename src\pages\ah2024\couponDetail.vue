<template>
  <div class="detail">
    <div class="top">
      <div class="top_left">
        <p class="top_left_title">购药优惠折扣（满18-10）</p>
        <p class="top_left_time" v-if="detail">有效期：{{ detail.effectTime.split(" ")[0] }} ~ {{ detail.expireTime.split(" ")[0] }}</p>
      </div>
    </div>
    <div class="main">
      <div class="main_box">
        <div v-show="source != 'cx'">
          <svg id="barcode" class="barcode"></svg>
          <p class="content">请出示条形码给线下门店<br>
            请勿将条形码发给他人以免被盗用</p>
        </div>
        <div v-show="source == 'cx'">
          <p v-show="countdown == 0 && detail.status == 'UNUSED'" class="refresh"><img src="~images/hzqjf2024/refresh.png" @click="refresh()">刷新</p>
          <img v-show="countdown > 0 && detail.status == 'UNUSED'" :src="qrCode" class="qrCode">
          <div v-show="countdown == 0 && detail.status == 'UNUSED'" class="qrCode_shadow" :style="{'background': 'url(' + qrCode + ') center center/100% no-repeat'}"></div>
          <p style="text-align: center;">{{ detail.promoCode }}</p>
          <p v-show="countdown > 0 && detail.status == 'UNUSED'" class="countdown"><span>{{ countdown }}s</span>&nbsp;&nbsp;后失效</p>
          <p class="content">请出示二维码给线下门店<br>
            请勿将二维码发给他人以免被盗用</p>
        </div>
      </div>
      <div class="main_box" style="padding-bottom: .14rem">
        <div class="title but_round">使用规则</div>
        <p class="rule_content">限权益人本人使用【{{cardInfo.bindName + '/' + cardInfo.bindIdNum}}】</p>
        <div v-if="detail.couponDesc" style="margin-top: .16rem">
          <p class="rule" v-for="(item, index) in detail.couponDesc.split(';')" :key="index">{{ item }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import QRCode from 'qrcode'
import JsBarcode from "jsbarcode";
import api from '@/api/cityHealth';
import { maskString } from '@/utils/tools'
export default {
  components: {},
  data() {
    return {
      source: this.$route.query.source || '',
      detail: "",
      cardInfo: "",
      qrCode: "",
      countdown: 60, // 倒计时
      cardNo: this.$route.query.cardNo || '',
      rightCode: this.$route.query.rightCode || '',
      id: this.$route.query.id || ''
    }
  },
  computed: {},
  watch: {},
  created() {
    this.submitBrand()
    this.queryCardList()
  },
  mounted() {},
  methods: {
    submitBrand() {
      api.submitBrand({
        cardNo: this.cardNo,
        source: this.source
      }).then((res) => {
        this.detail = res.result.filter((item) => item.id == this.id)[0];
        if(this.source != 'cx') {
          JsBarcode("#barcode", this.detail.promoCode);
        } else {
          QRCode.toDataURL(this.detail.promoCode).then((url) => {this.qrCode = url;console.log(url)})
          this.refresh()
        }
      })
    },
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList({ cardNo: this.cardNo }).then((res) => {
        res.result.forEach((ele) => {
          ele.bindIdNum = maskString(ele.bindIdNum, 3, 4)
        })
        this.cardInfo = res.result[0]
      })
    },
    refresh() {
      this.countdown = 60
      let sendTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(sendTimer)
          this.countdown = 0
        }
      }, 1000)
    }
  },
}
</script>
<style lang="less" scoped>
.detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient( 360deg, #FFFFFF 0%, #FFF9F6 100%);
  overflow: auto;
  .top {
    display: flex;
    align-items: center;
    margin: .2rem .12rem .16rem;
    .top_left {
      flex: 1;
      .top_left_title {
        margin-bottom: .04rem;
        font-size: .22rem;
        font-weight: bold;
        color: #FD5710;
        line-height: .3rem;
      }
      .top_left_time {
        font-size: .12rem;
        color: #666666;
        line-height: .18rem;
      }
    }
  }
  .main {
    flex: 1;
    overflow-y: auto;
    width: 3.73rem;
    background: rgba(255,255,255,0.5);
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    border-radius: .24rem .24rem 0 0;
    border: 1px solid #FFFFFF;
    .main_box {
      width: 3.51rem;
      position: relative;
      background: #FFFFFF;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.06);
      border-radius: .16rem;
      border: 1px solid #FFFFFF;
      margin: 0.12rem auto;
      .refresh {
        position: absolute;
        font-size: .14rem;
        font-weight: 500;
        color: #FD5710;
        line-height: .2rem;
        display: flex;
        align-items: center;
        right: .12rem;
        top: .12rem;
        img {
          width: .16rem;
          margin-right: .06rem;
        }
      }
      .barcode {
        display: block;
        margin: 0 auto;
      }
      .qrCode {
        width: 1.98rem;
        display: block;
        margin: .32rem auto 0;
        position: relative;
      }
      .qrCode_shadow {
        width: 1.98rem;
        height: 1.98rem;
        display: block;
        margin: .32rem auto 0;
        position: relative;
      }
      .qrCode_shadow::before {
        content: "已失效";
        position: absolute;
        display: block;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, .5);
        z-index: 1;
        font-size: .18rem;
        color: #fff;
        font-weight: 500;
        line-height: 1.98rem;
        text-align: center;
      }
      .countdown {
        font-size: .18rem;
        font-weight: 500;
        color: #222222;
        line-height: .25rem;
        text-align: center;
        margin-top: .12rem;
        span {
          color: #FD5710
        }
      }
      .content {
        font-size: .14rem;
        color: #666666;
        line-height: .2rem;
        font-weight: 400;
        text-align: center;
        margin: .12rem auto .2rem;
      }
      .title {
        font-size: 0.22rem;
        color: #222222;
        line-height: 0.3rem;
        display: flex;
        font-family: 'ShuHei-B';
        display: inline-block;
        margin: 0 .16rem;
      }
      .rule_content {
        font-size: .14rem;
        color: #F51A1A;
        line-height: .21rem;
        margin: .16rem .16rem 0;
      }
      .rule {
        font-size: .14rem;
        color: #666666;
        line-height: .21rem;
        margin: 0 .16rem 0.06rem
      }
    }
  }
}
</style>
