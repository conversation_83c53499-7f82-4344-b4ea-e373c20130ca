<template>
  <div align="center">
    <van-field
      v-bind="$attrs"
      v-on="$listeners"
      v-model="textValue"
      readonly
      clickable
      @click="showOptions"
      :is-link="!textValue"
      required
    />
    <!-- 选择器 -->
    <van-action-sheet v-bind="actionSheetProps" v-model="isShowActionSheet" :description="optionsTitle" :closeable="true" style="min-height: 80%">
      <div class="content" >
        <div class="condition" style="margin-top: -.04rem">
          <van-field label="药品名称" v-model="keyword"  placeholder="请输入药品名称" />
          <div @click.stop="queryHospital" class="search-btn">搜索</div>
        </div>
        <div style="margin-top: .45rem">
          <van-empty
            v-if="options.length === 0" 
            :image="require('../../../../assets/images/common/empty.png')"
            description="请搜索" 
          />
          <van-radio-group v-model="type" v-else>
            <van-cell-group :border="false">
              <div v-for="(item, index) in options" :key="index">
                <van-cell clickable @click="choosePayType(item)">
                  <template #title>
                    <span>{{item.name}}</span>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item.name" :checked-color="theme.primaryColor">
                      <template #icon="props">
                        <img class="img-icon" :src="props.checked ? require(`../../../../assets/images/icon/cicon-selected.png`) : require(`../../../../assets/images/icon/cicon-unselected.png`)" />
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </div>
            </van-cell-group>
          </van-radio-group>
        </div>
        
      </div>

      <div class="footer">
        <div class="btn" @click.stop="handleSelectOption()">确定</div>
      </div>
    </van-action-sheet>
    
  </div>
</template>

<script>
import { theme } from '@/mixin'
import api from '@/api/cityHealth'
export default {
  mixins: [theme],
  name: 'CxFieldSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
  },
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 数据选项对应的标签和值的key
    keyMap: {
      type: Array,
      default() {
        return ['name']
      }
    },
    // 对应actionSheet的description属性
    optionsTitle: {
      type: String,
      default: ''
    },
    // actionSheet组件的所有属性
    actionSheetProps: {
      type: Object,
      default() {
        return {}
      }
    },
    hospitalAddress: {
      type: String,
      default: ''
    },
    hospitalArea: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      type: "",
      keyword: "",
      options: [],
      isShowActionSheet: false,
    }
  },
  mounted() {
    this.queryHospital()
  },
  watch: {
  },
  computed: {
    // 当前选择项的lable(中文描述)
    textValue() {
      return this.value
    },
  },
  methods: {
    // 关键字查询药品
    queryHospital() {
      if(!this.keyword) {
        console.log('请输入药品名称')
        // this.$toast("请输入药品名称")
      } else {
        api.queryDrugName({name: this.keyword}).then(res => {
          this.options = res.result
        })
      }
    },
    // 显示选项弹窗
    showOptions() {
      if (!this.$attrs.disabled) {
        this.type = ""
        this.isShowActionSheet = true
      }
    },
    // 选择某一项时，返回该项的value（值），并隐藏actionSheet组件Z
    handleSelectOption() {
      if(this.type) {
        this.$emit('change', this.type)
        this.isShowActionSheet = false
      } else {
        this.$toast('请选择药品')
      }
    },
    choosePayType(item) {
      this.type = item[this.keyMap[0]]
    }
  }
}
</script>
<style lang="less" scoped>
.pharmacy-cell {
  /deep/.van-cell {
    padding: .13rem 0
  }
  /deep/.van-cell__title {
    flex:inherit;
    margin-right: 12px;
    width: 1rem;
    text-align: left;
  }
  /deep/.van-cell__value {
    text-align:left
  }
}
/deep/.van-action-sheet__description {
  font-size: .2rem;
  color: #222222
}
/deep/.van-field .van-cell__right-icon {
  bottom : .13rem;
  right: 0
}
/deep/.van-action-sheet__description::after {
  border-bottom: none
}
/deep/.van-cell::after {
  border-bottom: none
}
/deep/.van-empty {
  // margin-top: 1rem;
  .van-empty__image {
    width: 1.43rem;
      height: 1.34rem;
    display: flex;
  }
  .van-empty__description {
    margin-top: 0.24rem;
    font-size: .14rem;
    color: #999999;
  }
}
.content {
  padding-bottom: .8rem;
  .condition  {
    width: 3.35rem;
    height: .4rem;
    background: #ffffff;
    border: 1px solid #f4f4f4;
    border-radius: .21rem;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    overflow: hidden;
    position: absolute;
    top: .6rem;
    z-index: 10;
    left: 0.16rem;
    display: flex;
    align-items: center;
    padding: 0 .04rem;
    .search-btn {
      width: .6rem;
      height: .32rem;
      line-height: .32rem;
      background: @primary-color;
      border-radius: .16rem;
      box-shadow: 0px 2px 8px 0px rgba(157,0,76,0.10);
      color: #fff
    }
    /deep/.van-button__text {
      max-width: .55rem;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    /deep/.van-field {
      padding: 0 .12rem;
      width: 1.88rem;
      flex: 1
    }
    /deep/.van-cell::after {
      border-bottom: none
    }
    /deep/.van-field__label {
      border-right: 1px solid #DDDDDD !important;
      margin-right: 0.12rem !important;
      text-align: center !important;
    }
  }
  .img-icon {
    width: .16rem
  }
}

.footer {
  width: 100%;
  height: .8rem;
  border-top: 1px solid #F4F4F4;
  background: #fff;
  position: absolute;
  bottom: 0;
  left:0;
  z-index:1000;
  display: flex;
  align-items: center;
  justify-content: center;
  .btn {
    width: 3.43rem;
    height: .48rem;
    line-height: .48rem;
    background: @primary-color;
    border-radius: .24rem;
    color: #fff;
    font-size: .18rem
  }
}
</style>