<template>
  <div class="arrange">
    <atb-step style="width: 2.87rem" :options="stepList" :stepNum="'2'"></atb-step>
    <!-- 预约信息 -->
    <div class="arrange_box">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>预约信息</span>
        </div>
      </div>
      <div class="info">
        <img v-if="icon" :src="icon" alt="">
        <!-- <img :src="require(`../../assets/images/atb/swipe_${reserveParams.rightCode}.png`)"> -->
        <div style="margin-left: .12rem">
          <p style="color: #222222;font-weight: 500">{{rightsObj[reserveParams.rightCode]}}</p>
          <p>权益人：{{bindName}}</p>
          <p>证件类型：{{idTypeObj[bindIdType]}}</p>
          <p>证件号码：{{maskIdNum}}</p>
          <p>手机号：{{reserveParams.phoneNo}}</p>
        </div>
      </div>
    </div>

    <div class="footer">
      <van-button class="btn" @click="reserve">立即预约</van-button>
    </div>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import AtbStep from './components/AtbStep.vue'
import {rightsObj, idTypeObj} from './js/idType'
import { maskString } from '@/utils/tools'
export default {
  name: '',
  components: { AtbStep },
  data() {
    return {
      icon: "",
      reserveParams: {},
      rightsObj: rightsObj,
      idTypeObj: idTypeObj,
      bindName: this.$route.query.bindName || "",
      bindIdType: this.$route.query.bindIdType || "",
      bindIdNum: this.$route.query.bindIdNum || "",
      stepList: [
        {
          num: '1',
          name: '预约申请',
        },
        {
          num: '2',
          name: '预约安排',
        },
        {
          num: '3',
          name: '预约完成',
        },
      ],
    }
  },
  computed: {
    maskIdNum() {
      return maskString(this.bindIdNum, 3, 4)
    },
  },
  watch: {},
  created() {
    this.reserveParams = JSON.parse(this.$route.query.reserveParams) || {}
    this.queryRights()
  },
  mounted() {},
  methods: {
    // 查询权益
    queryRights() {
      api.pbmCardRights({ cardNo: this.reserveParams.cardNo }, { noLoading: true }).then((res) => {
        var obj = res.result.filter((item) => item.rightCode == this.reserveParams.rightCode)[0]
        this.icon = obj.icon
      })
    },
    reserve() {
      api.reserve(this.reserveParams).then(() => {
        this.$router.push('/health-atb-success')
      })
    }
  },
}
</script>
<style lang="less" scoped>
.arrange {
  .arrange_box {
    width: 3.43rem;
    background: #fff;
    border-radius: .12rem;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    margin: 0.24rem auto 1rem;
    padding: .16rem 0 0;
    .title {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.24rem;
      display: flex;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: #f34168;
          border-radius: 0.02rem;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    .info {
      display: flex;
      align-items: flex-start;
      padding: .16rem;
      img {
        width: .6rem;
        height: .6rem;
      }
      p {
        margin-bottom: .12rem;
        color: #666666;
        line-height: .2rem
      }
    }
  }
  .footer {
    width: 100%;
    height: 0.8rem;
    background: #ffffff;
    position: fixed;
    z-index: 10;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #F4F4F4;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      background: #F34168;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      font-weight: 500;
      border: none
    }
  }
}
</style>