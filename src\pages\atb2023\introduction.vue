<template>
  <div class="introduction">
    <!-- 顶部栏 -->
    <div class="title_top">
      <p>{{ rightDetail.rightName }}</p>
      <img src="~images/common/intro_top.png" />
    </div>
    <div v-if="rightDetail != {}" class="right_introduction">
      <!-- 权益介绍 -->
      <div v-if="intro" class="intro">
        <img src="~images/atb2023/intro-title.png" class="intro-img" />
        <p v-html="intro"></p>
      </div>
      <!-- 协议内容 -->
      <p class="agreement">
        权益详见<zcx-agreement-box :isInline="true" :queryParams="agreeUrlQuery" :color="textColor"/>
      </p>
      <!-- 预约流程 -->
      <div class="reserve_box">
        <div class="title">
          <div class="tit-con">
            <i></i>
            <span>预约流程</span>
          </div>
        </div>
        <atb-step :options="stepList" :stepNum="String(stepList.length)"></atb-step>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div class="footer">
      <van-button type="primary" class="btn" @click="getPvUv()">立即预约</van-button>
    </div>

    <!-- 协议弹窗 -->
    <zcx-agreement-popup v-model="agreePopup" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <keep-alive>
          <component v-bind:is="currentComponent" class="agreementBox"></component>
        </keep-alive>
      </div>
    </zcx-agreement-popup>

    <!-- 健康告知 -->
    <van-action-sheet v-model="healthShow" title="健康告知">
      <div style="max-height:3rem;overflow:auto">
        <div class="termContent" style="padding-bottom: .12rem">权益持有人目前或曾经患有下列疾病或症状：</div>
        <div class="termContent" style="padding-bottom: .12rem">肿瘤：恶性肿瘤（含原位癌）、白血病、脑或脊髓的肿瘤或占位</div>
        <div class="termContent">权益人如果针对以上健康告知有任何未如实告知，本公司有权解除合同，对于合同解除前发生的保障事故，不承担赔付责任。</div>
      </div>
      <div class="radio-block">
        <van-field label="请确认是否有以上健康问题">
          <template #input>
            <van-radio-group v-model="healthRadio" direction="horizontal">
              <van-radio name="1">
                无
                <template #icon="props">
                  <svg-icon class="img-icon" :iconName="props.checked ? 'radio_sel' : 'radio_unsel'" />
                </template>
              </van-radio>
              <van-radio name="0" style="margin-left: .2rem;margin-right: 0">
                有
                <template #icon="props">
                  <svg-icon class="img-icon" :iconName="props.checked ? 'radio_sel' : 'radio_unsel'" />
                </template>
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
      </div>
      <div class="termBtnBox">
        <van-button type="primary" class="iKnow" @click="healthCheck">{{ healthRadio == "0" ? '有部分问题' : '确认无以上问题' }}
        </van-button>
      </div>
    </van-action-sheet>

    <!-- 报错信息弹窗 -->
    <van-popup v-model="messShow" class="messDialog">
      <p class="title">激活失败</p>
      <p class="text">非常抱歉，权益人不符合健康告知审核</p>
      <van-button type="primary" class="iKnow" @click="messShow = false">我知道了</van-button>
    </van-popup>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import { theme } from '@/mixin'
import ZcxAgreementBox from '@/components/ZcxAgreementBox/index'
import atbStep from './components/step'
export default {
  mixins: [theme],
  components: {
    ZcxAgreementBox,
    atbStep
  },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"196",keys:"sysm"},
      textColor:"#F34168",
      cardNo: this.$route.query.cardNo || '', // 卡号
      rightCode: this.$route.query.rightCode || '', // 权益code
      rightDetail: {}, // 权益详情
      intro: {}, // 权益介绍
      currentName: '《爱她保健康管理服务使用说明》', // 协议名字
      currentComponent: 'atb2023_sysm', // 协议内容
      agreePopup: false, // 协议弹窗
      healthShow: false, // 健康告知弹窗
      messShow: false,
      healthRadio: "", // 健康告知选择
    }
  },
  computed: {
    stepList() {
      return [{name: '预约告知'},{name: '预约申请'},{name: '预约安排'},{name: '预约完成'}]
    },
  },
  mounted() {
    this.getRights()
  },
  methods: {
    getRights() {
      api.pbmCardRights({ cardNo: this.cardNo }).then((res) => {
        this.rightDetail = res.result.filter((item) => item.rightCode == this.rightCode)[0]
        // 查询权益介绍
        this.rightsIntro()
      })
    },
    // 查询权益介绍
    rightsIntro() {
      api.rightsIntro(this.rightCode, { noLoading: true }).then((res) => {
        this.intro = res.result && res.result.intro
      })
    },
    getPvUv() {
      api.getPvUv({
        eventOwner: this.rightDetail.cardTypeCode,
        eventCode: this.rightCode,
        eventType: 'RIGHTS',
      })
      .then(() => {
        this.to()
      })
    },
    to() {
      if(this.rightCode == 'PR355') {
        this.healthShow = true;
        console.log(this.healthRadio)
        return
      }
      this.$router.push({
        path: '/health-atb2023-reserve',
        query: {
          cardNo: this.cardNo,
          rightCode: this.rightCode,
        },
      })
    },
    call(tel) {
      window.location.href = 'tel:' + tel
    },
    // 校验健康告知及激活
    healthCheck() {
      if(this.healthRadio == '0') {
        this.healthShow = false;
        this.messShow = true
        this.healthRadio = "";
        return;
      }
      if(!this.healthRadio) {
        this.$toast("请确认是否有以上健康问题")
        return;
      }
      this.$router.push({
        path: '/health-atb2023-reserve',
        query: {
          cardNo: this.cardNo,
          rightCode: this.rightCode,
        },
      })
    },
  },
}
</script>
<style lang="less" scoped>
.introduction {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../../assets/images/common/com-bg.png') right top/1.36rem 1.4rem no-repeat , linear-gradient(360deg, #FFFFFF 0%, #FFF5F5 100%);
  overflow: auto;
  .title_top {
    display: flex;
    align-items: center;
    background: transparent;
    p {
      font-size: 0.2rem;
      line-height: 0.3rem;
      font-weight: bold;
      flex: 1;
      margin-left: 0.12rem;
      color: #EB2A5C;
    }
    img {
      width: 1.08rem;
      margin-left: 0.32rem;
    }
  }
  .right_introduction {
    flex: 1;
    width: 100%;
    border-radius: 0.24rem 0.24rem 0 0;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    height: 10vh;
    overflow-y: auto;
    .intro {
      padding: 0 0.16rem 0.16rem;
      .intro-img {
        width: 1.91rem;
        margin: 0.16rem auto 0.05rem;
        display: block;
      }
    }
    .old_intro {
      width: 3.43rem;
      margin: 0.16rem 0.16rem 0;
    }
    .agreement {
      text-align: center;
      color: #222222;
      margin-bottom: .2rem;
      span {
        color: #EB2A5C;
      }
    }
    .reserve_box {
      width: 3.43rem;
      height: 1.28rem;
      background: #fff;
      border-radius: 0.12rem;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
      margin: 0.16rem auto;
      padding: 0.16rem 0 0;
      .title {
        font-size: 0.18rem;
        font-weight: 500;
        line-height: 0.24rem;
        display: flex;
        .tit-con {
          flex: 1;
          display: flex;
          align-items: center;

          i {
            display: inline-block;
            vertical-align: middle;
            margin-right: 0.12rem;
            width: 0.04rem;
            height: 0.2rem;
            background: #EB2A5C;
            border-radius: 0 0.02rem 0.02rem 0;
          }

          span {
            vertical-align: middle;
            line-height: 0.26rem;
          }
        }
      }
    }
  }

  .footer {
    width: 100%;
    background: #ffffff;
    border-top: 1px solid #f4f4f4;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      font-weight: 500;
      border: none;
      padding: 0.12rem 0;
      margin: 0.16rem auto;
      display: block;
    }
    .btn_text {
      height: auto;
      background: #fff;
      border: none;
      font-size: 0.18rem;
      font-weight: 500;
      color: #EB2A5C;
      padding: 0;
      margin: 0 auto 0.16rem;
      display: block;
    }
  }

  .readPopup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .title {
      height: 0.6rem;
      box-sizing: border-box;
      line-height: 0.28rem;
      padding: 0.16rem;
      text-align: center;
      font-weight: 400;
      color: #333333;
      font-size: 0.16rem;
      position: relative;

      h2 {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
    }

    .agreementBox {
      flex: 1;
      overflow-y: scroll;
      margin-bottom: 0.1rem;
    }
  }
  .termContent {
    padding: 0 .16rem .16rem;
    margin: 0;
    font-size: .14rem;
    color: #666666;
    line-height: .22rem;
    text-indent: .28rem
  }

  .termBtnBox {
    border: 1px solid #F4F4F4;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: .16rem 0;

    .iKnow {
      width: 3.43rem;
      height: .48rem;
      background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
      border: none;
      border-radius: .24rem;
      font-size: .18rem;
      font-weight: 500;
    }
  }
  /deep/.radio-block {
    box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);

    .van-field {
      padding: .16rem;
    }

    .van-field .van-field__label {
      width: 2rem;
    }

    .van-field__control:read-only {
      display: block;
    }

    .van-radio-group--horizontal {
      justify-content: end;
    }

    .van-radio__icon {
      height: auto;
    }

    .img-icon {
      width: .2rem
    }
  }
  /deep/.van-action-sheet__header {
    padding: 0.24rem 0 0.16rem;
    font-size: .2rem;
    font-weight: bold;
    color: #222222;
    line-height: .24rem;
  }

  /deep/.van-action-sheet__close {
    top: 0.24rem
  }
  .messDialog {
    width: 3.11rem;
    background: #ffffff;
    border-radius: .16rem;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    .title {
      margin: .24rem auto .16rem;
      text-align: center;
      font-size: .2rem;
      font-weight: 500;
      line-height: .28rem
    }
    .text {
      font-size: .16rem;
      line-height: .24rem;
      padding: 0 .24rem;
      text-align: center;
    }
    .iKnow {
      width: 2.63rem;
      height: .48rem;
      border-radius: .24rem;
      margin: .48rem .24rem .24rem;
      border: none;
      background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
    }
  }
}
</style>
