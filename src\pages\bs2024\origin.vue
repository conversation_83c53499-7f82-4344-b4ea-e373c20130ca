<template>
  <div class="origin">
    <!-- 用户基本信息 -->
    <div class="userInfo">
      <img src="~images/hc2023/avatar.png" class="avatar" />
      <div v-show="currentCardInfo" style="flex: 1; margin-left: 0.14rem">
        <p class="phoneNo">{{ currentCardInfo.bindPhoneNo }}</p>
        <p class="toOrders" @click="toOrders">
          我的预约<img src="~images/hc2023/avatar-arrow.png" />
        </p>
      </div>
    </div>
    <!-- 当前所选权益人 -->
    <div class="currentInfo">
      <p class="nameANDidNum">权益人：{{ currentCardInfo.bindName }}&nbsp;/&nbsp;{{ maskIdNum }}</p>
      <van-button
        type="text"
        :icon="require('../../assets/images/icon/exchange.png')"
        class="exchange-btn"
        @click="holderShow = true"
        >切换</van-button
      >
    </div>
    <!-- 权益列表 -->
    <div class="rights_block">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>权益展示</span>
        </div>
      </div>
      <van-empty
        v-show="rightsList.length === 0"
        :image="require('../../assets/images/atb/empty.png')"
        :description="'您暂无此权益\n请激活权益后再次尝试'"
      />
      <div class="rightList" v-show="rightsList.length > 0">
        <van-collapse v-model="activeNames" :border="false">
          <van-collapse-item :name="n" v-for="(m, n) in rightsList" :key="n">
            <template #title>
              <div class="collapseTitle">
                <div class="collapse_circle"></div>{{ m.name }}
              </div>
            </template>
            <template #default>
              <div class="rightInfo" v-for="(item, index) in m.list" :key="index">
                <p class="count" v-show="item.maxUse > 0">剩余{{ item.maxUse - item.usedCount }}次</p>
                <div style="display: flex">
                  <img :src="item.icon" class="rightIcon" />
                  <div style="flex: 1; margin-top: 0.16rem; max-width: 1.86rem">
                    <p class="rightName">{{ item.rightName }}</p>
                    <div v-if="item.rightTips">
                      <p v-for="(m, n) in item.rightTips.split(' ')" :key="n" class="tips">{{ m }}</p>
                    </div>
                    <p class="endTime">有效期至{{ item.endTime.split(' ')[0] }}</p>
                  </div>
                  <van-button class="rightBtn" @click="reserve(item)" :disabled="item.status == 'DISABLED'">立即查看</van-button>
                </div>
              </div>
            </template>
          </van-collapse-item>
        </van-collapse>
      </div>
    </div>

    <!-- 权益人选择器 -->
    <van-action-sheet
      v-model="holderShow"
      description="权益人"
      :closeable="true"
      :close-icon="require('../../assets/images/icon/close.png')"
    >
      <div class="content" :style="{ 'max-height': height + 'px' }">
        <van-radio-group v-model="actionCardInfo">
          <van-cell-group :border="false">
            <div v-for="(item, index) in cardList" :key="index">
              <van-cell clickable>
                <template #title>
                  <span>{{ item.bindName }}</span>
                </template>
                <template #label>
                  <img src="~images/icon/idNum.png" class="img-icon" />
                  <p>{{ item.bindIdNum }}</p>
                </template>
                <template #right-icon>
                  <van-radio :name="item" checked-color="#EB2A5C" @click="confirmAction">
                  </van-radio>
                </template>
              </van-cell>
            </div>
          </van-cell-group>
        </van-radio-group>
      </div>

      <div class="footer">
        <van-button type="primary" class="btn" :to="'/health-bs2024-activation'">新增</van-button>
      </div>
    </van-action-sheet>

    <!-- 小程序 -->
    <van-popup v-model="miniShow" class="miniDialog">
      <p class="title">{{ miniName }}</p>
      <p class="text">赶紧去使用权益吧</p>
      <wx-open-launch-weapp id="launch-btn" :username="miniOrgId" :path="miniPath" style="margin: 0 auto;display: block;">
        <component :is="'script'" type="text/wxtag-template">
          <component :is="'style'">
            .btn { width: 263px; height: 48px; border-radius: 24px; margin: 24px 24px 24px; display:
            block; background: #DD0000; border: none; color: #fff; font-size: 16px }
          </component>
          <button class="btn">去{{ miniName }}领取权益</button>
        </component>
      </wx-open-launch-weapp>
    </van-popup>
  </div>
</template>
<script>
import wx from 'weixin-js-sdk'
import api_global from '@/api/global'
import api from '@/api/cityHealth'
import { maskString, groupArr } from '@/utils/tools'
import { theme } from '@/mixin'

export default {
  mixins: [theme],
  components: {},
  data() {
    return {
      cardTypeCode: '268', // 卡种
      cardList: [], // 权益卡列表
      rightsList: [], // 权益列表
      currentCardInfo: {}, // 当前权益人卡信息
      actionCardInfo: '', // 选择权益人项
      holderShow: false, // 选择权益人弹窗
      height: document.documentElement.clientHeight * 0.7,
      miniName: '', // 小程序名称
      miniOrgId: '', // 小程序id
      miniPath: '', // 小程序路径
      miniShow: false, // 小程序弹窗
      activeNames: [],
    }
  },
  computed: {
    maskIdNum() {
      return maskString(this.currentCardInfo.bindIdNum, 2, 4)
    },
  },
  watch: {},
  created() {
    this.signature()
    this.queryCardList()
  },
  mounted() {},
  methods: {
    // 预约权益
    reserve(event) {
      if (event.url && event.url.type == 'url') {
        location.href = event.url.path
          .replace('{cardNo}', event.cardNo)
          .replace('{idNum}', this.currentCardInfo.bindIdNum)
          .replace('{cardType}', this.currentCardInfo.cardTypeCode)
          .replace('{rightCode}', event.rightCode)
          .replace('{userId}', this.currentCardInfo.userId)
      } else if (event.url && event.url.type == 'miniApp') {
        this.miniName = event.url.name
        this.miniOrgId = event.url.orgId
        this.miniPath = event.url.path
        this.miniShow = true
      } else {
        this.$router.push({
          path: '/health-bs2024-introduction',
          query: {
            cardNo: event.cardNo,
            rightCode: event.rightCode,
          },
        })
      }
    },
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList({ cartTypeCode: this.cardTypeCode }).then((res) => {
        if (res.result.length == 0) {
          this.$router.push({
            path: '/health-bs2024-activation',
          })
          return
        }
        res.result.forEach((ele) => {
          ele.bindPhoneNo = maskString(ele.bindPhoneNo, 3, 4)
        })
        this.cardList = res.result
        this.currentCardInfo = this.actionCardInfo = this.cardList[0]
        this.queryRights()
      })
    },
    // 查询权益
    queryRights() {
      api
        .pbmCardRights({ cardNo: this.currentCardInfo.cardNo }, { noLoading: true })
        .then((res) => {
          res.result.forEach((ele) => {
            ele.url = ele.url ? JSON.parse(ele.url) : ele.url
          })
          this.rightsList = groupArr(res.result, 'catName')
          this.activeNames = Array.from({ length: this.rightsList.length }).map((item, index) => {
            return index
          })
        })
    },
    toOrders() {
      this.$router.push('/health-common-orders')
    },
    // 确认选择当前权益人
    confirmAction() {
      this.currentCardInfo = this.actionCardInfo
      this.queryRights()
      this.holderShow = false
    },
    // 微信菜单
    signature() {
      const currentUrl = window.location.href.split('#')[0]
      api_global.signature({ url: currentUrl }).then((res) => {
        const configInfo = (res && res.result) || {}
        wx.config({
          debug: false,
          appId: configInfo.appId,
          timestamp: configInfo.timestamp.toString(),
          nonceStr: configInfo.nonceStr,
          signature: configInfo.signature,
          jsApiList: ['wx-open-launch-weapp'],
          openTagList: ['wx-open-launch-weapp'],
        })

        wx.ready((res) => {
          console.log('ready:', res)
        })
        wx.error((res) => {
          console.log('jssdk error')
          console.log(res)
        })
      })
    },
  },
}
</script>
<style lang="less" scoped>
.origin {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../../assets/images/common/com-bg.png') right top/1.36rem 1.4rem no-repeat,
    linear-gradient(360deg, #ffffff 0%, #fff5f5 100%);
  overflow: auto;
  .userInfo {
    display: flex;
    align-items: center;
    padding: 0.18rem 0.14rem;
    .avatar {
      width: 0.6rem;
      height: 0.6rem;
    }
    .phoneNo {
      background-image: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      -webkit-background-clip: text;
      color: transparent;
      -webkit-text-fill-color: transparent;
      font-size: 0.18rem;
      line-height: 0.24rem;
      margin-bottom: 0.08rem;
    }
    .toOrders {
      color: #666666;
      line-height: 0.2rem;
      font-size: 0.14rem;
      display: flex;
      align-items: center;
      max-width: 0.86rem;
      img {
        width: 0.16rem;
        margin-left: 0.06rem;
      }
    }
  }
  .currentInfo {
    padding: 0.12rem;
    margin: 0 0.24rem;
    background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
    border-radius: 0.2rem 0.2rem 0px 0px;
    display: flex;
    align-items: center;
    .nameANDidNum {
      flex: 1;
      color: #fff;
      font-size: 0.14rem;
      line-height: 0.2rem;
    }
    .exchange-btn {
      background: transparent;
      border: none;
      padding: 0;
      color: #fff;
      font-size: 0.12rem;
      line-height: 0.18rem;
      height: auto;
    }
  }
  .rights_block {
    flex: 1;
    border-radius: 0.24rem 0.24rem 0 0;
    background: rgba(255, 255, 255, 0.5);
    padding: 0.2rem 0.12rem;
    position: relative;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .title {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.22rem;
      display: flex;
      margin-bottom: 0.16rem;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: #eb2a5c;
          border-radius: 0.02rem;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    .center-btn {
      background: transparent;
      border: none;
      padding: 0;
      color: #eb2a5c;
      font-size: 0.12rem;
      line-height: 0.18rem;
      height: auto;
      position: absolute;
      top: 0.23rem;
      right: 0.12rem;
      z-index: 10;
    }
    .rightList {
      flex: 1;
      overflow-y: auto;
      .rightInfo {
        width: 3.48rem;
        padding: 0;
        margin: 0 auto 0.12rem;
        background: #ffffff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
        border-radius: 0.12rem;
        .count {
          width: 0.56rem;
          height: 0.2rem;
          background: #32b9bc;
          border-radius: 0.12rem 0 0.12rem 0;
          color: #fff;
          font-size: 0.12rem;
          line-height: 0.2rem;
          text-align: center;
        }
        .rightIcon {
          width: 0.7rem;
          height: 0.7rem;
          margin: 0.06rem 0 0.09rem 0;
        }
        .rightName {
          line-height: 0.2rem;
          font-weight: bold;
          font-size: 0.14rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          color: #222222;
        }
        .tips {
          background: #f3f8ff;
          border-radius: 0.02rem;
          color: #eb2a5c;
          line-height: 0.14rem;
          padding: 0.02rem 0.04rem;
          margin-right: 0.08rem;
          display: inline-block;
          vertical-align: middle;
          margin-top: 0.06rem;
          font-size: 0.1rem;
        }
        .endTime {
          font-size: 0.12rem;
          color: #666666;
          line-height: 0.18rem;
          padding: 0.06rem 0 0.1rem;
        }
        .rightBtn {
          width: 0.8rem;
          height: 0.28rem;
          background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
          box-shadow: 0px 2px 8px 0px rgba(235, 42, 92, 0.2);
          border-radius: 0.16rem;
          padding: 0;
          border: none;
          color: #fff;
          font-weight: bold;
          margin-top: 0.3rem;
        }
      }
      .collapseTitle {
        padding: 0 .12rem;
        position: relative;
        font-size: .16rem;
        font-weight: bold;
        background-image: -webkit-linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .collapseTitle::before {
        content: "";
        position: absolute;
        width: .16rem;
        height: .16rem;
        border-radius: 50%;
        background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
        opacity: 0.2;
        left: 0.02rem;
      }
      .collapse_circle {
        position: absolute;
        left: 0;
        top: -0.02rem;
        width: 0.08rem;
        height: 0.08rem;
        border-radius: 50%;
        background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
        opacity: 0.8;
      }
      .collapse_circle::before {
        background: #ffffff;
        width: .04rem;
        height: .04rem;
        border-radius: 50%;
        position: absolute;
        content: "";
        display: block;
        left: .02rem;
        top: .02rem;
      }
      /deep/.van-cell {
        background: transparent;
        padding: .2rem 0 .12rem;
      }
      /deep/.van-cell::after {
        border-bottom: none;
      }
      /deep/.van-collapse-item__content {
        padding: 0;
        background: transparent;
      }
      /deep/.van-collapse-item--border::after {
        border-top: none
      }
    }
  }

  .content {
    .van-cell-group {
      padding: 0 0.16rem;
      padding-bottom: 0.8rem;
    }
    .van-cell {
      background: #fafafa;
      border-radius: 0.12rem;
      overflow: hidden;
      margin-bottom: 0.12rem;
      padding: 0.12rem 0.16rem;
    }
    .van-cell__title,
    .van-cell__value {
      font-size: 0.14rem;
      font-weight: 500;
      color: #222222;
      line-height: 0.2rem;
    }
    .van-cell__label {
      font-size: 0.12rem;
      color: #666666;
      font-weight: 400;
      line-height: 0.16rem;
      display: flex;
      align-items: center;
      margin-top: 0.08rem;
    }
    .img-icon {
      width: 0.16rem;
      margin-right: 0.04rem;
    }
  }

  .footer {
    width: 100%;
    height: 0.8rem;
    border-top: 1px solid #f4f4f4;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      border: none;
    }
  }
  .miniDialog {
    width: 3.11rem;
    background: #ffffff;
    border-radius: 0.16rem;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    .title {
      margin: 0.24rem auto 0.16rem;
      text-align: center;
      font-size: 0.2rem;
      font-weight: 500;
      line-height: 0.28rem;
    }
    .text {
      font-size: 0.16rem;
      line-height: 0.24rem;
      padding: 0 0.24rem 0.24rem;
      text-align: center;
    }
  }
  /deep/.van-empty {
    .van-empty__image {
      width: 2.5rem;
      height: 1.81rem;
      display: flex;
    }
    .van-empty__description {
      margin-top: 0.24rem;
      font-size: 0.14rem;
      color: #999999;
      white-space: pre-wrap;
      text-align: center;
    }
  }
  /deep/.van-action-sheet__description {
    font-size: 0.2rem;
    line-height: 0.28rem;
    font-weight: 500;
    color: #222222;
    padding: 0.2rem 0;
  }
  /deep/.van-action-sheet__description::after {
    border-bottom: none;
  }
}
</style>
