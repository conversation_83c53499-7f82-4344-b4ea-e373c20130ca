<template>
  <div class="introduction com-bg">
    <!-- 顶部栏 -->
    <div class="title_top">
      <p>{{ rightDetail.rightName }}</p>
      <img src="~images/common/intro_top.png" />
    </div>
    <div v-if="rightDetail != {}" class="right_introduction">
      <!-- 权益介绍 -->
      <div class="intro">
        <img src="~images/hc2023/intro-title.png" class="intro-img" />
        <p v-html="intro"></p>
      </div>
      <!-- 协议内容 -->
      <p class="agreement">
        权益详见<zcx-agreement-box :isInline="true" :queryParams="agreeUrlQuery" :color="textColor"/>
      </p>
      <!-- 预约流程 -->
      <div class="reserve_box" v-show="!rightDetail.contactService && !rightDetail.url">
        <div class="title">
          <div class="tit-con">
            <i></i>
            <span>预约流程</span>
          </div>
        </div>
        <atb-step :options="stepList" :stepNum="String(stepList.length)"></atb-step>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div
      class="footer"
      v-show="!( urlObj.type == 'noButton')"
    >
      <van-button type="primary" class="btn" @click="getPvUv()">
        {{ urlObj.type == 'contactService' ||rightDetail.contactService?  '联系客服': '立即预约'}}
      </van-button>
      <van-button :style="{'margin-top': rightCode == 'PR304' ? '.16rem' : '0'}"
        v-show="rightDetail.contactService&&urlObj.type !='contactService'"
        type="text"
        class="btn_text"
        @click="call('4000133558')"
        >咨询客服</van-button
      >
    </div>

    <!-- 协议弹窗 -->
    <zcx-agreement-popup v-model="agreePopup" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <keep-alive>
          <component v-bind:is="currentComponent" class="agreementBox"></component>
        </keep-alive>
      </div>
    </zcx-agreement-popup>

    <van-popup v-model="showPop" class="dialog" :close-on-click-overlay="false">
      <img src="~images/hc2023/wxCallCenter.png" class="wxCallCenter" />
      <img src="~images/icon/close.png" class="close" @click="showPop = false" />
    </van-popup>

    <!-- 联系客服 -->
    <van-popup v-model="callCenterShow" position="bottom" class="callCenter_dialog" closeable>
      <div v-for="(item, index) in callCenterObj[this.rightCode]" :key="index">
        <p class="call_title">
          <span class="call_circle"></span>
          {{ item.title }}
        </p>
        <p class="url_label" v-show="item.label">{{ item.label }}</p>
        <p class="url_content">{{ item.content }}</p>
        <img v-show="item.qrCode" :src="item.qrCode" class="url_qrCode">
      </div>
    </van-popup>
  </div>
</template>
<script>
import { callCenterObj } from '../healthService/js/callCenter'
import api from '@/api/cityHealth'
import { theme } from '@/mixin'
import ZcxAgreementBox from '@/components/ZcxAgreementBox/index'
import atbStep from '../atb2023/components/step'
export default {
  mixins: [theme],
  components: {
    ZcxAgreementBox,
    atbStep
  },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"268",keys:"sysm"},
      textColor:"#DD0000",
      cardNo: this.$route.query.cardNo || '', // 卡号
      rightCode: this.$route.query.rightCode || '', // 权益code
      rightDetail: {contactService:null}, // 权益详情
      intro: '', // 权益介绍
      currentName: '《百惠保健康管理服务使用说明》', // 协议名字
      currentComponent: 'bs2024_sysm', // 协议内容
      agreePopup: false, // 协议弹窗
      showPop: false,
      callCenterShow: false,
      callCenterObj,
      urlObj:{},
    }
  },
  computed: {
    stepList() {
      return [{name: '预约告知'},{name: '预约申请'},{name: '预约安排'},{name: '预约完成'}]
    },
  },
  mounted() {
    this.getRights()
  },
  methods: {
    getRights() {
      api.pbmCardRights({ cardNo: this.cardNo }).then((res) => {
        this.rightDetail = res.result.filter((item) => item.rightCode == this.rightCode)[0]
        if(this.rightDetail&&this.rightDetail.url&&this.rightDetail.url.length>0){
          this.urlObj = JSON.parse(this.rightDetail.url)
        }
        // 查询权益介绍
        this.rightsIntro()
      })
    },
    // 查询权益介绍
    rightsIntro() {
      api.rightsIntro(this.rightCode, { noLoading: true }).then((res) => {
        this.intro = res.result && res.result.intro
      })
    },
    getPvUv() {
      api.getPvUv({
        eventOwner: this.rightDetail.cardTypeCode,
        eventCode: this.rightCode,
        eventType: 'RIGHTS',
      })
      .then(() => {
        this.to()
      })
    },
    to() {
      if (this.rightDetail.contactService && !this.rightDetail.url) {
        this.showPop = true
      } else if(this.urlObj.type == 'contactService') {
        if(this.urlObj.onlyTel) {
          window.location.href = 'tel:' + this.urlObj.tel
        } else {
          this.callCenterShow = true
        }
      } else {
        this.$router.push({
          path: '/health-common-reserve',
          query: {
            cardNo: this.cardNo,
            rightCode: this.rightCode,
          },
        })
      }
    },
    call(tel) {
      console.log(tel)
      window.location.href = 'tel:' + tel
    },
  },
}
</script>
<style lang="less" scoped>
.introduction {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(360deg, #FFFFFF 0%, #FFF5F5 100%);
  .title_top {
    display: flex;
    align-items: center;
    background: transparent;
    p {
      font-size: 0.2rem;
      line-height: 0.3rem;
      font-weight: bold;
      flex: 1;
      margin-left: 0.12rem;
      color: #DD0000;
    }
    img {
      width: 1.08rem;
      margin-left: 0.32rem;
    }
  }
  .right_introduction {
    flex: 1;
    width: 100%;
    border-radius: 0.24rem 0.24rem 0 0;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    height: 10vh;
    overflow-y: auto;
    .intro {
      padding: 0 0.16rem 0.16rem;
      .intro-img {
        width: 1.91rem;
        margin: 0.16rem auto 0.05rem;
        display: block;
      }
    }
    .old_intro {
      width: 3.43rem;
      margin: 0.16rem 0.16rem 0;
    }
    .agreement {
      text-align: center;
      color: #222222;
      span {
        color: #DD0000;
      }
    }
    .reserve_box {
      width: 3.43rem;
      height: 1.28rem;
      background: #fff;
      border-radius: 0.12rem;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
      margin: 0.16rem auto;
      padding: 0.16rem 0 0;
      .title {
        font-size: 0.18rem;
        font-weight: 500;
        line-height: 0.24rem;
        display: flex;
        .tit-con {
          flex: 1;
          display: flex;
          align-items: center;

          i {
            display: inline-block;
            vertical-align: middle;
            margin-right: 0.12rem;
            width: 0.04rem;
            height: 0.2rem;
            background: #DD0000;
            border-radius: 0 0.02rem 0.02rem 0;
          }

          span {
            vertical-align: middle;
            line-height: 0.26rem;
          }
        }
      }
    }
  }

  .footer {
    width: 100%;
    background: #ffffff;
    border-top: 1px solid #f4f4f4;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      font-weight: 500;
      border: none;
      padding: 0.12rem 0;
      margin: 0.16rem auto;
      display: block;
    }
    .btn_text {
      height: auto;
      background: #fff;
      border: none;
      font-size: 0.18rem;
      font-weight: 500;
      color: #DD0000;
      padding: 0;
      margin: 0 auto 0.16rem;
      display: block;
    }
  }

  .readPopup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .title {
      height: 0.6rem;
      box-sizing: border-box;
      line-height: 0.28rem;
      padding: 0.16rem;
      text-align: center;
      font-weight: 400;
      color: #333333;
      font-size: 0.16rem;
      position: relative;

      h2 {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
    }

    .agreementBox {
      flex: 1;
      overflow-y: scroll;
      margin-bottom: 0.1rem;
    }
  }
  .dialog {
    width: 3.75rem;
    height: 100%;
    background: transparent;
    .wxCallCenter {
      width: 3.35rem;
      margin: 1rem 0.2rem 0.14rem;
    }
    .close {
      width: 0.32rem;
      margin: 0 1.71rem;
    }
  }
  .callCenter_dialog {
    width: 3.75rem;
    // height: 512px;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    border-radius: .24rem .24rem 0px 0px;
    .call_title {
      padding: 0.15rem .36rem .08rem 0.28rem;
      position: relative;
      font-size: .16rem;
      font-weight: bold;
      background-image: -webkit-linear-gradient(90deg, #1669E3 0%, #493EE1 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .call_title::before {
      content: "";
      position: absolute;
      width: .16rem;
      height: .16rem;
      border-radius: 50%;
      background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      opacity: 0.2;
      left: 0.18rem;
      top: .16rem;
    }
    .call_circle {
      position: absolute;
      left: .16rem;
      top: 0.14rem;
      width: 0.08rem;
      height: 0.08rem;
      border-radius: 50%;
      background: linear-gradient(90deg, #F65D5E 0%, #DD1924 100%);
      opacity: 0.8;
    }
    .call_circle::before {
      background: #ffffff;
      width: .04rem;
      height: .04rem;
      border-radius: 50%;
      position: absolute;
      content: "";
      display: block;
      left: .02rem;
      top: .02rem;
    }
    .url_title {
      padding: 0 .2rem;
      font-size: .14rem;
      color: #222222;
      line-height: .21rem;
      font-weight: 500;
    }
    .url_label {
      padding: 0 .2rem .08rem;
      font-size: .14rem;
      color: #222222;
      line-height: .21rem;
      font-weight: bold;
    }
    .url_content {
      padding: 0 .2rem .15rem;
      font-size: .14rem;
      color: #222222;
      line-height: .21rem;
      font-weight: 400;
      white-space: pre-wrap;
    }
    .url_qrCode {
      width: 1.22rem;
      display: block;
      margin: 0 auto .2rem;
    }
  }
}
</style>
