const proData = {
  /******  皮肤数据  ******/
  "primaryColor": "#1669E3", // 主颜色（如：按钮背景颜色） #fe3132
  "primaryBgColor": "rgba(23, 102, 228, 1)", // 页面背景色
  "subBgColor": "rgba(0,93,204,0.05)", // 次要颜色（如：浅色背景色）
  "btnBgColor": "linear-gradient(90deg, #1669E3 0%, #493EE1 100%)", // 通用按钮背景色

  /******  产品信息（城市城的相关信息）******/
  proName: "宸汐健康管家", // 产品名称
  cartCode: "100", // cart项目code
  wisdomCode: "101", // 绿通项目code
  hpvCode: "102", // hpv女神卡项目code
  atbCode: "103", // 爱她保
  hybCode: "104", // 惠邕保
  jhbCode: "105", // 京惠保
  dhbCode: "106", // 达惠保
  hpvV2Coed: "107", // hpv2期——未上线
  cpipCode: "108", // 太保
  TY_A_code: "109", // 特药A方案
  TY_B_code: "110", // 特药B方案
  TY_C_code: "111", // 特药C方案
  TY_D_code: "112", // 特药D方案
  TY_D_PLUS_code: "113", // 特药D_plus方案
  TY_D_PRO_code: "114", // 特药D_pro方案
  TY_D_PRO_PLUS_code: "115", // 特药D_pro_plus方案
  // 公众号信息
  name: "宸汐健康管家",
  appId: "", // 生产
  testAppId: "", // 测试
  
  // 分享信息
  shareTitle: "", // 分享标题
  shareDesc: "", // 分享描述

  policyStatus: {
    '00': "待回传",
    '01': "成功",
    '02': "失败",
    '03': "退保发起",
    '04': "退保中",
    '05': "已退保"
  },
  idTypeList: [{
    name: "身份证",
    value: 1
  },{
    name: "护照",
    value: 2
  },{
    name: "港澳居民来往内地通行证",
    value: 3
  },{
    name: "台胞证",
    value: 4
  }],
  idTypeObj: {
    1: "身份证",
    2: "护照",
    3: "港澳居民来往内地通行证",
    4: "台胞证",
  },
}

export default proData
