<template>
  <van-popup v-model="show" v-bind="popupProps" @close="handleClose" :style="{ height: '80%', 'border-radius': '.2rem .2rem 0 0' }">
    <div class="popup-content" ref="popupContent">
      <slot></slot>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'CxAgreementPopup',
  model: {
    prop: 'value',
    event: 'change'
  },
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    value: {
      type: Boolean,
      default: false
    },
    // 如果内部有滚动条，关闭时内容是否回滚到顶部
    colseScrollTop: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false // van-popup的显示隐藏
    }
  },
  computed: {
    // 合并所有属性，并设置默认属性值
    // 默认属性有 position: "bottom", closeable: true, closeIcon: "clear", style: { height: '80%' }
    popupProps() {
      // 合并样式
      let style = {
        height: '80%',
        ...this.$attrs.style
      }
      // 合并所有属性
      return {
        position: 'bottom',
        closeable: true,
        closeIcon: "clear",
        ...this.$attrs,
        style
      }
    }
  },
  watch: {
    value(val) {
      this.show = val
    }
  },
  methods: {
    handleClose() {
      this.$emit('change', false)

      // 关闭时，内容回滚到顶部
      this.colseScrollTop && this.$refs.popupContent.scrollTo(0, 0)
    }
  }
}
</script>

<style lang="less" scoped>
.popup-content {
  height: 100%;
  overflow-y: auto;
  background: #fff;
}
</style>
