<template>
  <div class="reserve">
    <div class="rights_box" style="margin-top: 0.2rem">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>预约权益</span>
        </div>
      </div>
      <div class="rightCode">
        <van-field v-model="rightName" readonly :disabled="true" />
      </div>
    </div>

    <div class="rights_box" style="padding-bottom: .16rem;margin-bottom: 1rem">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>权益人信息</span>
        </div>
      </div>
      <van-form ref="personInfo">
        <van-field v-model="bindName" label="姓名" disabled required/>
        <van-field v-model="idTypeObj[bindIdType]" label="证件类型" disabled required/>
        <van-field v-model="maskIdNum" label="证件号码" disabled required/>
        <van-field
          v-model="reserveParams.phoneNo"
          label="手机号"
          placeholder="请输入手机号"
          clearable
          :maxlength="11"
          required
          :rules="validateRules.phoneNoRules"
          error-message="*请填写当前可用的手机号预约！"
        />
      </van-form>
    </div>

    <div class="footer">
      <van-button class="btn" @click="update">提交修改</van-button>
    </div>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import { maskString } from '@/utils/tools'
import { idTypeObj } from './js/idType'
import validateRules from '@/utils/fieldValidateRules'
export default {
  name: '',
  components: {},
  data() {
    return {
      bindName: "",
      bindIdType: "",
      idTypeObj: idTypeObj,
      bindIdNum: "",
      reserveParams: {
        cardNo: this.$route.query.cardNo,
        phoneNo: "",
        rightCode: "",
        id: this.$route.query.id || ""
      },
      rightName: '', // 所选权益名称
      validateRules
    }
  },
  computed: {
    maskIdNum() {
      return maskString(this.bindIdNum, 3, 4)
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.queryOrderDetail()
  },
  methods: {
    // 查询预约详情
    queryOrderDetail() {
      var id = this.$route.query.id || ""
      api.queryOrderDetail({id: id}).then((res) => {
        var obj = res.result
        this.bindName = obj.pbmCardUser.bindName
        this.bindIdType = obj.pbmCardUser.bindIdType || 1
        this.bindIdNum = obj.pbmCardUser.bindIdNum
        this.reserveParams.phoneNo = obj.phoneNo
        this.reserveParams.rightCode = obj.rightCode
        this.rightName = obj.rightName
      })
    },
    update() {
      this.$refs.personInfo.validate().then(() => {
        var params = this.reserveParams
        api.updateReserve(params).then(() => {
          this.$router.replace('/health-atb-supplementSuccess')
        })
      })
    }
    // to() {
    //   this.$refs.personInfo.validate().then(() => {
    //     this.$router.push({
    //       path: "/health-atb-arrange",
    //       query: {
    //         reserveParams: JSON.stringify(this.reserveParams),
    //         bindName: this.bindName,
    //         bindIdType: this.bindIdType,
    //         bindIdNum: this.bindIdNum
    //       }
    //     })
    //   }).catch(err => {
    //     console.info('校验失败,具体内容如下：',err)
    //     window.scrollTo(0, this.$refs.personInfo.$el.offsetTop)
    //     this.$toast(err[0].message)
    //   })
    // }
  },
}
</script>
<style lang="less" scoped>
.reserve {
  .rights_box {
    width: 3.43rem;
    background: #fff;
    border-radius: 0.12rem;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    margin: 0 auto 0.12rem;
    padding: 0.16rem 0 0;
    .title {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.24rem;
      display: flex;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: #f34168;
          border-radius: 0.02rem;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    /deep/.rightCode {
      padding-bottom: 0.16rem;
      .van-field {
        padding: 0.1rem 0.16rem;
        width: 3.11rem;
        border: 1px solid #dcdcdc;
        border-radius: 0.08rem;
        margin: 0.16rem 0.16rem 0;
      }
      .van-cell__right-icon {
        color: #f34168;
      }
    }
    /deep/.van-form {
      padding: 0 .16rem;
      .van-field {
        padding: .16rem 0
      }
      .van-field .van-field__label {
        width: .88rem;
        margin-right: 0;
        color: #666666;
        white-space: pre-line;
      }
      .van-cell::after {
        left: 0;
        right: 0
      }
      .van-field .van-field__error-message {
        bottom: -.16rem
      }
    }
    /deep/.van-cell--required::before {
      position:relative;
      left:0;
      margin-right: 0.04rem;
    }
  }
  .footer {
    width: 100%;
    height: 0.8rem;
    background: #ffffff;
    position: fixed;
    z-index: 10;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #F4F4F4;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      background: #F34168;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      font-weight: 500;
      border: none
    }
  }
}
</style>