<template>
    <div class="origin">
        <!-- 项目介绍 -->
        <div class="origin_head">
            <div class="head_top">
                <img class="top_img" :src="require(`@/assets/images/com/logo_${productName}.png`)">
                <div class="top_text">{{ productObj[cardTypeCode] }}</div>
            </div>
            <!-- 用户信息-激活前 -->
            <div class="head_bottom" v-if="!isActivation" @click="toBinding">
                <div class="bottom_box">
                    <img class="bottom_left_img" src="~images/ga2024/ren.png">
                    <div class="bottom_left_text">绑定被保人，立即开通健康增值服务</div>
                </div>
                <img class="bottom_right_img" src="~images/ga2024/right.png">
            </div>
            <!-- 用户信息-激活后 -->
            <div class="origin_user" v-else>
                <!-- 权益人 -->
                <div class="people">
                    <div class="item_people" v-for="(item, index) in cardPeoList" :key="index" @click="choose(item)"
                        :style="{ background: nowPeoName === item.name ? bgColor : 'rgba(229, 68, 65, 0.06)', color: nowPeoName === item.name ? '#FFFFFF' : '#444444' }"
                        :ref="'people-' + index" v-show="index < 3">
                        <img :src="item.peopleImg" class="peopleImg">
                        <div class="name">{{ item.name }}</div>
                        <div v-if="nowPeoName === item.name" class="arrow-down"></div>
                    </div>
                    <div class="item_people" style="border: none;" @click="isChoose = true">
                        <svg-icon class="chooseImg" iconName="choose" :color="color" />
                        <div class="name" :style="{ color: color }">新增/切换</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 权益卡信息 -->
        <div class="interestsAccount_content">
            <!-- 轮播图 -->
            <swiper :options="swiperOption" ref="swiperBox" class="display: flex;">
                <swiper-slide v-for="(item, index) in nowPeoCardList" :key="index" class="content_swiper">
                    <div class="item_agree">
                        <img class="agree_img" src="~images/ga2024/book.png" style="display: block;">
                        <div class="agree_text" @click="showPdf(item)">服务手册</div>
                    </div>
                    <div class="swiper_item">
                        <div class="item_title">{{ productObj[item.cardTypeCode] }}&健康增值服务</div>
                        <div class="item_label" v-show="item.cardEndTime">有效期至：{{ item.cardEndTime }}日
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
        </div>
        <div class="main">
            <img class="main_bg" :src="require(`@/assets/images/com/mainBg_${productName}.png`)">
            <div class="user">
                <!-- 导航栏 -->
                <div class="tab">
                    <div class="item_tab" v-for="(m, n) in rightsList" :key="n"
                        :style="{ background: activeIndex === n ? color : '#FFFFFF', color: activeIndex === n ? '#FFFFFF' : '#444444' }"
                        @click="scrollToSection(n)">
                        {{ m.name }}
                    </div>
                </div>
                <div class="rights" v-for="(m, n) in rightsList" :key="n" :ref="`module-${n}`">
                    <div class="catName" v-show="m.name">
                        <span>{{ m.name }}</span>
                    </div>
                    <div class="rightInfo" v-for="(item, index) in m.list" :key="index">
                        <p class="count" v-show="item.maxUse > 0" :style="{ background: color }">
                            {{ item.maxUse - item.usedCount }}次</p>
                        <div style="display: flex;align-items: flex-start;">
                            <img :src="item.icon" class="rightIcon" />
                            <div style="flex: 1; margin-top: 0.04rem;max-width: 1.68rem;padding-right: .12rem;">
                                <p class="rightName">{{ item.rightName }}</p>
                                <div v-if="item.rightTips">
                                    <p v-for="(m, n) in item.rightTips.split(' ')" :key="n" class="tips">{{ m }}</p>
                                </div>
                            </div>
                            <van-button class="rightBtn" v-if="!isActivation" @click="showRightsIntro(item)"
                                :style="{ background: bgColor, boxShadow: bgBoxShadow }">
                                去使用</van-button>
                            <van-button class="rightBtn" @click="reserve(item)" v-else
                                :style="{ background: bgColor, boxShadow: bgBoxShadow }"
                                :disabled="item.status == 'DISABLED' || item.disabled || waitStatus">
                                {{ waitStatus || item.status == 'DISABLED' || item.disabled ? (item.noUsedCount ? '已用完'
                                    : '未生效')
                                    : '去使用'
                                }}</van-button>
                        </div>
                        <p class="endTime" v-show="item.endTime">有效期:{{ item.startTime ? item.startTime + '日' + '~' : ''
                        }} {{
                                item.endTime }}日
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部导航栏 -->
        <div class="tabFooter">
            <van-tabbar :active-color="color" inactive-color="#999999" route>
                <van-tabbar-item replace to="/health-com-origin">
                    <template #icon="props">
                        <svg-icon class="img-icon" iconName="home_in" :color="props.color" />
                    </template>
                    健康服务
                </van-tabbar-item>
                <van-tabbar-item :to="'/health-com-center?cardTypeCode=' + cardTypeCode">
                    <template #icon="props">
                        <svg-icon class="img-icon" iconName="center" :color="props.color" />
                    </template>
                    个人中心
                </van-tabbar-item>
            </van-tabbar>
        </div>
        <!-- 权益介绍 -->
        <van-popup v-model="isIntro" position="bottom"
            :style="{ height: '80%', width: '100%', 'border-radius': '0.24rem 0.24rem 0 0' }">
            <div class="choose_popup">
                <div class="popup_title">权益介绍</div>
                <img class="popup_close" src="~images/ga2024/close.png" @click="isIntro = false">
                <div class="popup_list">
                    <div v-html="intro"></div>
                </div>
                <div class="popup_button_box">
                    <div class="button_box_button" :style="{ background: bgColor, boxShadow: bgBoxShadow }"
                        @click="toBinding" style="font-size: 0.17rem;">
                        绑定被保人，立即开通健康增值服务
                    </div>
                </div>
            </div>
        </van-popup>
        <!-- 服务手册弹窗 -->
        <zcx-agreement-popup v-model="pdfShow" :colseScrollTop="true">
            <div class="readPopup">
                <div class="title">{{ currentName }}</div>
                <div class="agreement">
                    <iframe v-if="!isPDF" :src="currentComponent" class="htmlBox" frameborder="0"></iframe>
                    <pdf v-else class="pdfBox" v-for="item in numPages" :key="item" :src="currentComponent"
                        :page="item"></pdf>
                </div>
            </div>
        </zcx-agreement-popup>
        <!-- 绑定被保人 -->
        <van-popup v-model="isChoose" position="bottom"
            :style="{ height: '80%', width: '100%', 'border-radius': '0.24rem 0.24rem 0 0' }">
            <div class="choose_popup">
                <div class="popup_title">新增/切换被保人</div>
                <img class="popup_close" src="~images/ga2024/close.png" @click="isChoose = false">
                <div class="popup_list">
                    <div class="list_item" v-for="(item, index) in cardPeoList"
                        :style="{ border: nowPeoName === item.name ? '1px solid #E54441' : '1px solid #F5F5F5', background: nowPeoName === item.name ? '#FDFAF9' : '#FFFFFF' }"
                        :key="index" @click="chooseUserItem(item)">
                        <div class="item_left">
                            <img class="left_img" :src="item.peopleImg">
                            <div class="left_info">
                                <div class="info_user">
                                    <div class="user_name">{{ item.name }}</div>
                                    <div class="relationship" v-relationship="item.list[0].relation"
                                        v-show="nowPeoName === item.name">
                                        {{ relationObj[item.list[0].relation] }}
                                    </div>
                                </div>
                                <div class="info_id">
                                    <img class="id_img" src="~images/ga2024/id.png">
                                    <div class="id_number">
                                        {{ item.list[0].bindMaskIdNum }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <svg-icon class="item_right" iconName="thePeople" :color="color"
                            v-if="nowPeoName === item.name" />
                    </div>
                </div>
                <div class="popup_button_box">
                    <div class="button_box_button" :style="{ background: bgColor, boxShadow: bgBoxShadow }"
                        @click="toBinding">
                        未找到？点此绑定被保人</div>
                </div>
            </div>
        </van-popup>
        
        <!-- 小程序 -->
        <van-popup v-model="miniShow" class="miniDialog">
            <p class="title">{{ miniName }}</p>
            <p class="text">赶紧去使用权益吧</p>
            <wx-open-launch-weapp id="launch-btn" :username="miniOrgId" :path="miniPath" :appid="miniAppId"
                style="margin: 0 auto;display: block;">
                <component :is="'script'" type="text/wxtag-template">
                    <component :is="'style'">
                        .btn {
                        width: 263px;
                        height: 48px;
                        border-radius: 24px;
                        margin: 24px auto 24px;
                        background: linear-gradient(90deg, #1669E3 0%, #493EE1 100%);
                        border: none;
                        color: #fff;
                        font-size: 16px;
                        display: block;
                        }
                    </component>
                    <button class="btn">去{{ miniName }}领取权益</button>
                </component>
            </wx-open-launch-weapp>
        </van-popup>
    </div>
</template>
<script>
import wx from 'weixin-js-sdk'
import api from '@/api/cityHealth'
import api_user from '@/api/user'
import api_global from '@/api/global'
import pdf from 'vue-pdf'
import { swiper, swiperSlide } from 'vue-awesome-swiper'
import 'swiper/dist/css/swiper.css';
// import ZcxAgreementPopup from '@/components/ZcxAgreementPopup'
import { productObj, relationObj } from './js/dictCode'
import { interchangeZh, groupArr, maskString, groupPopArr } from '@/utils/tools'
import { getValueByKeyFromURL, getColor, getBgColor, getBoxShadow } from '@/utils/tools'

export default {
    components: {
        swiper: swiper,
        swiperSlide: swiperSlide,
        // ZcxAgreementPopup,
        pdf
    },
    data() {
        return {
            productName: getValueByKeyFromURL(location.href, 'productName') || '',
            fromPage: null, // 页面跳转来源-初始化为 null
            isActivation: false, // 是否激活
            // pdf查看
            pdfShow: false,
            isPDF: false,
            numPages: '', // pdf页数
            // 协议弹窗
            agreePopup: false,
            currentName: '', // 协议名字
            currentComponent: '', // 协议内容
            // 小程序
            miniName: '', // 小程序名称
            miniOrgId: '', // 小程序id
            miniPath: '', // 小程序路径
            miniAppId: '',
            miniShow: false, // 小程序弹窗
            // 权益人信息
            isChoose: false, // 选择权益人弹窗
            height: document.documentElement.clientHeight * 0.7,
            centerShow: false,
            // 卡信息
            cardPeoList: {}, // 权益人信息
            cardList: [], // 卡列表信息
            currentCardInfo: [], // 默认刷新、跳转后页面信息
            currentPeoInfo: [], // 存储即当前权益人信息
            actionPeoInfo: [],
            // 当前信息
            nowPeoName: '', // 当前选中的人
            nowPeoCardList: [], // 当前选中人的卡信息
            nowCardList: [], // 当前展示的卡信息
            nowCardCode: '',
            nowCardNo: '',
            cardTypeCode: getValueByKeyFromURL(location.href, 'cardTypeCode') || '', // 当前卡种
            actionCardInfo: [], // 当前卡信息
            rightsList: [], // 当前权益卡权益信息
            activeNames: [], //
            // 轮播图
            cardImgItem: 0, // 卡序号
            swiperOption: {
                slidesPerView: 'auto', // 宽度适应
                centeredSlides: true, // 是否居中
                initialSlide: this.cardImgItem, // 初始索引
                scrollbar: { draggable: true }, // 是否启动滚动条
            },
            // 导航选中的
            activeIndex: 0,
            // 绑定权益人弹窗
            intro: '',
            isIntro: false,
            // 导入
            productObj,
            relationObj,
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if (from.path != '/') {
                vm.fromPage = true
                console.log('从其他页面跳转', vm.fromPage)
            } else {
                vm.fromPage = false
                console.log('页面刷新', vm.fromPage)
            }
        });
    },
    computed: {
        cardTypeCodes() {
            var codes = []
            switch (this.productName) {
                case 'tm2025':
                    codes = ['449']
                    break
                default:
                    codes = {}
            }
            return codes
        },
        waitStatus() {
            if (this.currentCardInfo.needWait && this.currentCardInfo.status == 'AVILABLE') {
                return true
            } else {
                return false
            }
        },
        color() {
            return getColor(this.productName)
        },
        bgColor() {
            return getBgColor(this.productName)
        },
        bgBoxShadow() {
            return getBoxShadow(this.productName)
        },
    },

    directives: {
        // 关系颜色指令
        relationship: function (el, binding) {
            let background = "";
            let color = "";
            let borderColor = "";
            switch (binding.value) {
                case '11':
                    color = '#FF8C00'
                    borderColor = '#FF8C00'
                    background = '#FEFAFA'
                    break
                case '12':
                    color = '#E92614'
                    borderColor = '#E92614'
                    background = '#FEFAFA'
                    break
                case '99':
                    color = '#E54441'
                    borderColor = '#E54441'
                    background = '#F7FAFE'
                    break
                default:
                    color = '#E54441'
                    borderColor = '#E54441'
                    background = '#F7FAFE'
            }
            el.style.background = background
            el.style.color = color
            el.style.borderColor = borderColor
        },
    },
    created() {
        this.signature()
        this.queryCardList()
    },
    methods: {
        // 查询权益卡列表
        queryCardList() {
            api.queryCardList({ cardTypeCode: this.cardTypeCode }).then((res) => {
                // 当前项目有效的卡
                var arr = res.result.filter(item => item.status != 'UNAVILABLE')
                console.log("arr-------", arr)
                // 是否有绑定的卡
                this.isActivation = arr.length != 0
                // 没有此项目激活的卡
                if (arr.length === 0) {
                    this.nowPeoCardList = [{
                        cardTypeCode: this.cardTypeCode
                    }]
                    this.cardImgItem = 0
                    this.nowCardCode = this.cardTypeCode
                    this.$refs.swiperBox.swiper.slideTo(1, 200, false)
                    this.getRights(this.cardTypeCode, '')
                    return
                }
                // 有此项目激活的卡
                arr.forEach((ele) => {
                    ele.bindMaskPhoneNo = maskString(ele.bindPhoneNo, 3, 4)
                    ele.bindMaskIdNum = maskString(ele.bindIdNum, 3, 3)
                    ele.cardEndTime = interchangeZh(ele.endTime)
                })
                // 此项目所有的卡信息
                this.cardList = arr
                // 人对应的所有信息
                this.cardPeoList = groupPopArr(arr, 'bindName')
                console.log("this.cardList", this.cardList, this.cardPeoList)
                // 展示存储
                if (localStorage.getItem('health_com_cardInfo') && !this.$route.query.cardNo) { // 本页面刷新
                    const nowPeoCard = this.cardPeoList.filter(item => item.name == localStorage.getItem('nowPeoName'))
                    if (nowPeoCard.length != 0) {
                        this.nowPeoName = nowPeoCard[0].name
                        this.nowPeoCardList = nowPeoCard[0].list
                        this.nowCardList = nowPeoCard[0].list[0]
                        this.nowCardCode = this.cardTypeCode = nowPeoCard[0].list[0].cardTypeCode
                        this.nowCardNo = nowPeoCard[0].list[0].cardNo
                    } else {
                        this.nowPeoName = this.cardPeoList[0].name
                        this.nowPeoCardList = this.cardPeoList[0].list
                        this.nowCardList = this.cardPeoList[0].list[0]
                        this.nowCardCode = this.cardTypeCode = this.cardPeoList[0].list[0].cardTypeCode
                        this.nowCardNo = this.cardPeoList[0].list[0].cardNo
                    }
                    console.log('this.nowCardList+11111111', this.nowCardList)
                } else if (this.$route.query.cardNo && this.fromPage) { // 其他页面来的
                    this.nowCardList = this.cardList.filter(item => item.cardNo == this.$route.query.cardNo)[0]
                    this.nowPeoName = this.nowCardList.bindName
                    const nowPeoCard = this.cardPeoList.filter(item => item.name == this.nowPeoName)
                    this.nowPeoCardList = nowPeoCard[0].list
                    this.nowCardCode = this.cardTypeCode = nowPeoCard[0].list[0].cardTypeCode
                    this.nowCardNo = nowPeoCard[0].list[0].cardNo
                    console.log('this.nowCardList+22222222', this.nowCardList)
                } else {
                    // 当前选中的人
                    this.nowPeoName = this.cardPeoList[0].name
                    this.nowPeoCardList = this.cardPeoList[0].list
                    this.nowCardList = this.cardPeoList[0].list[0]
                    this.nowCardCode = this.cardTypeCode = this.cardPeoList[0].list[0].cardTypeCode
                    this.nowCardNo = this.cardPeoList[0].list[0].cardNo
                    console.log('nowPeoCardList+333333333', this.nowPeoName, this.nowPeoCardList)
                }
                this.currentCardInfo = this.nowCardList
                this.getRights(this.nowCardCode, this.nowCardNo)
            })
        },
        // 获取权益列表
        getRights(cardTypeCode, cardNo) {
            api.pbmCardRights({ cardTypeCode: cardTypeCode, cardNo: cardNo }, { noLoading: true }).then((res) => {
                res.result.forEach((ele) => {
                    ele.startTime = interchangeZh(ele.startTime)
                    ele.endTime = interchangeZh(ele.endTime)
                    ele.url = ele.url ? JSON.parse(ele.url) : ele.url
                    if (ele.status != 'DISABLED' && ele.status != 'EXPR') {
                        // 使用次数
                        if (ele.maxUse > 0 && ele.maxUse <= ele.usedCount) {
                            ele.disabled = true
                            ele.noUsedCount = true
                        } else {
                            ele.disabled = false
                        }
                    } else {
                        ele.disabled = true
                    }
                })
                this.rightsList = res.result
                console.log("this.currentCardInfo", this.currentCardInfo, "this.actionCardInfo", this.actionCardInfo)
                if (this.currentCardInfo.status == 'Expired') {
                    this.rightsList == []
                }
                console.log("ele==============", res.result)
                if (this.currentCardInfo.status == 'UNAVILABLE') {
                    this.rightsList = []
                    this.activeNames = []
                } else {
                    this.rightsList = groupArr(res.result, 'catName')
                    console.log("this.rightsList", this.rightsList)
                    this.activeNames = Array.from({ length: this.rightsList.length }).map((item, index) => {
                        return index
                    })
                }
            })
        },
        // 确认选择当前权益人
        chooseUserItem(item) {
            console.log("item", item)
            this.nowPeoName = item.name
            this.nowPeoCardList = item.list
            this.nowCardList = item.list[0]
            this.nowCardCode = this.cardTypeCode = item.list[0].cardTypeCode
            this.nowCardNo = item.list[0].cardNo
            this.cardImgItem = 0
            localStorage.setItem('nowPeoName', item.name)
            localStorage.setItem('health_com_cardPeoInfo', JSON.stringify(this.nowPeoCardList))
            localStorage.setItem('health_com_cardInfo', JSON.stringify(this.nowCardList))
            this.getRights(this.nowCardCode, this.nowCardNo)
            this.isChoose = false
        },
        // 点头像选择
        choose(item) {
            console.log("item", item)
            this.nowPeoName = item.name
            this.nowPeoCardList = item.list
            this.nowCardList = item.list[0]
            this.nowCardCode = this.cardTypeCode = item.list[0].cardTypeCode
            this.nowCardNo = item.list[0].cardNo
            this.cardImgItem = 0
            localStorage.setItem('nowPeoName', item.name)
            localStorage.setItem('health_com_cardPeoInfo', JSON.stringify(this.nowPeoCardList))
            localStorage.setItem('health_com_cardInfo', JSON.stringify(this.nowCardList))
            this.getRights(this.nowCardCode, this.nowCardNo)
        },
        // 点击导航跳转
        scrollToSection(index) {
            this.activeIndex = index
            this.$nextTick(() => {
                const element = this.$refs[`module-${index}`][0]
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                }
            });
        },
        // 预约权益
        reserve(event) {
            if (event.url && event.url.type == 'url' && event.url.introduction == 'N') {
                location.href = event.url.path
                    .replace('{cardNo}', event.cardNo)
                    .replace('{idNum}', this.currentCardInfo.bindIdNum)
                    .replace('{cardType}', this.currentCardInfo.cardTypeCode)
                    .replace('{rightCode}', event.rightCode)
                    .replace('{userId}', this.currentCardInfo.userId)
            } else if (event.url && event.url.type == 'miniApp' && event.url.introduction == 'N') {
                this.miniName = event.url.name;
                this.miniOrgId = event.url.orgId;
                this.miniPath = event.url.path;
                this.miniAppId = event.url.appId;
                this.miniShow = true
            } else {
                this.$router.push({
                    path: '/health-common-introduction',
                    query: {
                        cardNo: event.cardNo,
                        rightCode: event.rightCode,
                    },
                })
            }
        },
        // 去绑卡
        toBinding() {
            this.$router.push({
                path: '/health-com-binding',
                query: {
                    cardTypeCode: this.cardTypeCode
                }
            })
        },
        // 显示弹窗-未绑卡
        showRightsIntro({ rightCode }) {
            api.rightsIntro(rightCode, { noLoading: true }).then((res) => {
                this.intro = res.result && res.result.intro
                this.isIntro = true
            })
        },
        showPdf(value) {
            console.log("000000000000000", value)
            api.agreementList({ cardTypeCode: value.cardTypeCode, key: 'sysm' }).then((res) => {
                console.log(res)
                this.currentName = res.result[0].name
                this.currentComponent = res.result[0].url
                const pathname = new URL(this.currentComponent).pathname
                this.isPDF = pathname.split('.').pop() == 'pdf'
                console.log("this.isPDF", this.isPDF)
                if (this.isPDF) {
                    this.previewPdf()
                } else {
                    this.pdfShow = true
                }
            })
        },
        previewPdf() {
            this.currentComponent = pdf.createLoadingTask(this.currentComponent)
            this.currentComponent.promise.then(pdf => {
                this.$nextTick(() => {
                    this.numPages = pdf.numPages; // pdf总页数
                    console.log("this.numPages", this.numPages)
                    this.pdfShow = true
                });
            }).catch(() => {
                setTimeout(() => {
                    this.$toast("请在手机端打开")
                }, 1500);
            })
        },
        // 微信菜单
        signature() {
            const currentUrl = window.location.href.split('#')[0]
            api_global.signature({ url: currentUrl }).then((res) => {
                const configInfo = (res && res.result) || {}
                wx.config({
                    debug: false,
                    appId: configInfo.appId,
                    timestamp: configInfo.timestamp.toString(),
                    nonceStr: configInfo.nonceStr,
                    signature: configInfo.signature,
                    jsApiList: ['wx-open-launch-weapp'],
                    openTagList: ['wx-open-launch-weapp'],
                })
                wx.ready((res) => {
                    console.log('ready:', res)
                })
                wx.error((res) => {
                    console.log('jssdk error')
                    console.log(res)
                })
            })
        },
        // 查询登录信息
        registered() {
            api_user.registered({
                jumpSource: '',
                routing: '',
                extraParams: '',
            }).then((res) => {
                if (!res.result.registed) {
                    this.$router.push({
                        path: '/login',
                        query: {
                            returnUrl: encodeURIComponent(location.href),
                        },
                    })
                    return
                }
            })
        },
    }
}
</script>
<style lang="less" scoped>
.origin {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(180deg, #FFFFFF 0%, #FFF9F6 100%);
    overflow-x: hidden;

    .origin_head {
        padding: 0.1rem;
        box-sizing: border-box;
        background: #FFFFFF;

        .head_top {
            padding-bottom: 0.12rem;
            display: flex;
            align-items: center;

            .top_img {
                width: 0.4rem;
                height: 0.4rem;
            }

            .top_text {
                padding-left: 0.1rem;
                font-family: 'ShuHei-B';
                font-weight: bold;
                font-size: 0.16rem;
                color: #222222;
                line-height: 0.2rem;
            }
        }

        .head_bottom {
            width: 3.51rem;
            height: 0.28rem;
            padding: 0 0.08rem 0 0.1rem;
            box-sizing: border-box;
            background: #FEEAD2;
            box-shadow: 0rem 0.02rem 0.04rem 0rem rgba(253, 233, 190, 0.2);
            border-radius: 0.2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .bottom_box {
                display: flex;
                align-items: center;

                .bottom_left_img {
                    width: 0.13rem;
                    height: 0.14rem;
                }

                .bottom_left_text {
                    padding-left: 0.06rem;
                    font-size: 0.13rem;
                    color: #9A4914;
                    line-height: 0.14rem;
                }
            }

            .bottom_right_img {
                width: 0.14rem;
                height: 0.14rem;
            }
        }

        .origin_user {
            display: flex;
            justify-content: space-between;

            .people {
                display: flex;
                overflow-x: scroll;
                white-space: nowrap;

                .item_people {
                    height: 0.36rem;
                    border-radius: 0.24rem;
                    border: 1px solid #EEEEEE;
                    margin-right: 0.08rem;
                    opacity: 0.7;
                    display: flex;
                    align-items: center;
                    padding: 0 0.1rem;
                    margin-bottom: 0.1rem;
                    color: #222222;
                    position: relative; // 为了箭头定位
                    cursor: pointer;

                    .peopleImg {
                        width: 0.2rem;
                    }

                    .chooseImg {
                        width: 0.16rem;
                    }

                    .name {
                        font-size: 0.14rem;
                        line-height: 0.22rem;
                        padding-left: 0.06rem;
                    }
                }

                .arrow-down {
                    position: absolute;
                    bottom: -0.04rem;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 0.06rem solid transparent;
                    border-right: 0.06rem solid transparent;
                    border-top: 0.06rem solid #E54441;
                }
            }
        }
    }

    .interestsAccount_content {

        .content_swiper {
            position: relative;
            width: 3.19rem;
            height: 1.2rem;
            background: linear-gradient(153deg, #FEECD5 0%, #F8D5AE 100%);
            box-shadow: 0 0.02rem 0.04rem 0 rgba(248, 213, 174, 0.2);
            border-radius: 0.16rem;

            .swiper_item {
                height: 100%;
                background: url('../../assets/images/zy2024/cardInfo_bg.png') top 0.34rem right 0.12rem;
                background-color: transparent;
                background-repeat: no-repeat;
                background-size: 0.87rem 0.8rem;
                padding: 0.16rem;

                .item_title {
                    color: #9A4914;
                    font-family: 'ShuHei-B';
                    font-size: 0.18rem;
                    line-height: 0.24rem;
                }

                .item_label {
                    font-size: 0.13rem;
                    color: #6E2F1D;
                    line-height: 0.18rem;
                    padding-top: 0.12rem;
                }
            }

            .item_agree {
                background: rgba(255, 255, 255, 0.4);
                border-radius: 0 0.1rem 0 0.1rem;
                padding: 0.04rem 0.08rem;
                display: flex;
                align-items: center;
                position: absolute;
                right: 0;

                .agree_img {
                    width: 0.14rem;
                    height: 0.16rem;
                }

                .agree_text {
                    font-size: 0.14rem;
                    color: #6E2F1D;
                    line-height: 0.18rem;
                    padding-left: 0.06rem;
                }
            }
        }
    }

    .main {
        position: relative;

        .main_bg {
            width: 3.75rem;
            margin-top: -0.4rem;
            position: absolute;
            top: 0;
            left: 0;
            object-fit: cover;
            z-index: 1;
        }

        .user {
            padding: 0 0.12rem 0.2rem;
            position: relative;
            z-index: 2;

            .tab {
                display: flex;
                padding: 0.11rem 0 0.16rem;

                .item_tab {
                    background-color: #FFFFFF;
                    color: #444444;
                    border-radius: 0.08rem;
                    padding: 0.06rem 0.1rem;
                    margin-right: 0.08rem;
                    font-size: 0.14rem;
                    line-height: 0.18rem;
                }

                .item_tab.active {
                    background: #E54441;
                    color: #FFFFFF;
                    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
                }
            }

            .rights {
                background: #FFFFFF;
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
                border-radius: 0.16rem;
                padding-bottom: 0.01rem;
                margin-bottom: 0.12rem;

                .catName {
                    padding: 0.16rem;
                    position: relative;
                    background: linear-gradient(180deg, #FFEFEF 0%, #FFFFFF 100%);
                    border-radius: 0.16rem 0.15rem 0 0;
                    text-align: center;

                    span {
                        font-weight: bold;
                        font-size: .18rem;
                        line-height: .24rem;
                        color: #222222;
                    }
                }

                .rightInfo {
                    margin: 0 0.12rem 0.12rem;
                    padding: 0.16rem 0.12rem 0.2rem;
                    position: relative;
                    background: #FFFBFA;
                    border-radius: 0.12rem;

                    .count {
                        width: 0.28rem;
                        height: 0.14rem;
                        color: #fff;
                        font-size: 0.1rem;
                        line-height: 0.14rem;
                        text-align: center;
                        border-radius: .07rem .07rem .07rem 0;
                        position: absolute;
                        left: .42rem;
                        top: 0.1rem;
                    }

                    .rightIcon {
                        width: 0.56rem;
                        height: 0.56rem;
                        margin-right: 0.08rem;
                    }

                    .rightName {
                        line-height: 0.2rem;
                        color: #222222;

                        font-size: 0.14rem;
                        white-space: pre-line;
                        display: flex;
                        align-items: center;
                    }

                    .tips {
                        background: #FFF9F2;
                        border-radius: 0.04rem;
                        color: #C07F31;
                        line-height: 0.16rem;
                        padding: 0.02rem 0.04rem;
                        margin-right: 0.04rem;
                        display: inline-block;
                        vertical-align: middle;
                        margin-top: 0.08rem;
                        font-size: 0.12rem;
                    }

                    .endTime {
                        font-size: 0.12rem;
                        color: #666666;
                        line-height: 0.16rem;
                        padding: 0.04rem 0 0 0.64rem;
                    }

                    .rightBtn {
                        width: 0.74rem;
                        height: 0.32rem;
                        border-radius: 0.28rem;
                        padding: 0;
                        border: none;
                        color: #fff;
                        font-weight: bold;
                        margin-top: 0.12rem;
                    }
                }
            }
        }
    }

    // 底部导航栏
    .tabFooter {
        height: 0.8rem;
        position: relative;
        z-index: 999;

        .van-tabbar--fixed {
            height: 0.8rem;
        }

        .van-tabbar-item {
            font-size: 0.1rem;
            line-height: 0.12rem;
        }

        .img-icon {
            width: 0.24rem;
        }
    }

    .choose_popup {
        position: relative;
        height: 100%;

        .popup_title {
            padding: 0.2rem 0 0.16rem 0;
            box-sizing: border-box;
            font-weight: bold;
            font-size: 0.2rem;
            color: #222222;
            line-height: 0.32rem;
            text-align: center;
        }

        .popup_close {
            position: absolute;
            top: 0.28rem;
            right: 0.16rem;
            width: 0.16rem;
            height: 0.16rem;
        }

        .popup_list {
            width: 100%;
            height: calc(100% - 0.68rem);
            overflow-y: auto;
            padding-bottom: 0.88rem;
            box-sizing: border-box;
            padding-left: 0.16rem;
            padding-right: 0.16rem;

            .list_item {
                border-radius: 0.1rem;
                padding: 0.12rem 0.2rem 0.12rem 0.16rem;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 0.12rem;

                .item_left {
                    display: flex;
                    align-items: center;

                    .left_img {
                        width: 0.4rem;
                        height: 0.45rem;
                    }

                    .left_info {
                        padding-left: 0.12rem;

                        .info_user {
                            display: flex;
                            align-items: center;

                            .user_name {
                                font-size: 0.16rem;
                                color: #222222;
                                line-height: 0.24rem;
                            }

                            .relationship {
                                display: inline-block;
                                height: .21rem;
                                line-height: .21rem;
                                font-size: .14rem;
                                border-style: solid;
                                border-width: .01rem;
                                padding: 0 .06rem;
                                border-radius: .06rem;
                                margin-left: 0.12rem;
                            }
                        }

                        .info_id {
                            padding-top: 0.07rem;
                            display: flex;
                            align-items: center;

                            .id_img {
                                width: 0.16rem;
                                height: 0.16rem;
                            }

                            .id_number {
                                padding-left: 0.04rem;
                                font-size: 0.12rem;
                                color: #666666;
                                line-height: 0.16rem;
                            }
                        }
                    }
                }

                .item_right {
                    width: 0.23rem;
                    height: 0.2rem;
                }
            }
        }

        .popup_button_box {
            position: absolute;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            z-index: 99;
            height: 0.88rem;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);

            .button_box_button {
                width: 3.27rem;
                height: 0.48rem;
                border-radius: 0.28rem;
                font-weight: bold;
                font-size: 0.2rem;
                color: #FFFFFF;
                line-height: 0.48rem;
                text-align: center;
            }
        }
    }

    .readPopup {
        height: 100%;
        display: flex;
        flex-direction: column;
        margin-bottom: 0.6rem;

        .title {
            height: 0.6rem;
            box-sizing: border-box;
            line-height: 0.28rem;
            padding: 0.16rem;
            text-align: center;
            font-weight: 400;
            color: #333333;
            font-size: 0.18rem;
            position: relative;
        }

        .agreement {
            width: 3.75rem;
            min-height: 6.4rem;
            overflow-y: scroll;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;

            .htmlBox {
                width: 100%;
                min-height: 6.4rem;
            }

            .pdfBox {
                width: 120%;
                min-height: 6.4rem;
            }
        }
    }
    .miniDialog {
        width: 3.11rem;
        background: #ffffff;
        border-radius: 0.16rem;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);

        .title {
            margin: 0.24rem auto 0.16rem;
            text-align: center;
            font-size: 0.2rem;
            font-weight: 500;
            line-height: 0.28rem;
        }

        .text {
            font-size: 0.16rem;
            line-height: 0.24rem;
            padding: 0 0.24rem 0.24rem;
            text-align: center;
        }
    }
}
</style>