<template>
  <div>
    <van-field
      v-model="valueSub"
      readonly
      :label="label"
      :placeholder="placeholder"
      :disabled="this.$attrs.disabled"
      :clickable="!this.$attrs.disabled"
      :is-link="!this.$attrs.disabled"
      @click="showPopup"
      :required="required"
      :rules="fieldRule"
    />

    <!-- 选择地区弹窗 -->
    <van-popup v-model="isShowPopup" position="bottom" style="border-top-left-radius: 16px;border-top-right-radius: 16px;">
      <van-cascader
        v-model="cascaderValue"
        :title="pompTitle"
        :options="areaList"
        :field-names="fieldNames"
        @close="isShowPopup = false"
        @finish="onFinish"
        :active-color="color"
      />
    </van-popup>
  </div>
</template>

<script>
import { Cascader } from 'vant'
import { areaList } from '../../../components/CxFieldArea/area.js'
export default {
  components: {
    'van-cascader': Cascader,
  },
  name: 'CxFieldArea',
  model: {
    prop: 'value',
    event: 'change'
  },
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    value: {
      type: [String],
      default: () => {
        // return new Date()
        return ''
      }
    },
    // van-field 组件常用属性
    label: {
      type: [String],
      default: ''
    },
    placeholder: {
      type: [String],
      default: ''
    },
    pompTitle:{
      type: [String],
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    fieldRule: {
      type: [Array],
      default: () => {
        return []
      }
    },
    color: {
      type: String,
      default: '#EB2A5C'
    }
  },
  data() {
    return {
      areaList:areaList,
      cascaderValue: '',
      fieldNames: {
        text: 'label',
        value: 'value',
        children: 'children',
      },
      isShowPopup: false,
    }
  },
  computed: {
    valueSub() {
      return this.value
    }
  },
  created () {

  },
  methods: {
    // 显示日期弹窗
    showPopup() {
      if (!this.$attrs.disabled) {
        this.isShowPopup = true
      }
    },
    // 全部选项选择完毕后，会触发 finish 事件
    onFinish({ selectedOptions }) {
      this.isShowPopup = false
      this.fieldValue = selectedOptions[2].label
      this.$emit('change', selectedOptions.map((option) => option.label).join(''))
      this.$emit('getCode', selectedOptions.map((option) => option.label))
    },
  }
}
</script>
<style lang="less" scoped>
/deep/.van-cascader__header {
  margin-top: 0.1rem;
}
/deep/.van-cascader__title {
  margin: 0 auto;
  font-weight: 500;
}
/deep/.van-cascader__tabs.van-tabs--line .van-tabs__wrap {
  height: 0.4rem;
}
/deep/.van-tabs__line {
  width: 0.2rem;
}
/deep/.van-cascader__tabs .van-tab {
  font-weight: 500;
}
/deep/.van-cascader__option--selected {
  color: #EB2A5C;
}
/deep/.van-cascader__close-icon {
  color: #222222;
}
</style>
