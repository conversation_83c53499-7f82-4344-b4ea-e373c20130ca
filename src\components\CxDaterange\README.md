## 时间选择组件弹窗

由Popup 和DatetimePicker组件组合而成。

### 组件属性
属性包括DatetimePicker的所有属性，和本组件特定属性

[DatetimePicker的官方属性](https://youzan.github.io/vant/#/zh-CN/datetime-picker#props)

**本组件特定属性如下：**

|参数	|说明	|类型	|默认值|
|--|--|--|--|
|label |Field的label属性 | string | '' |
|placeholder |Field的placeholder属性 | string | '' |
|isFormatDate | 是否需要把返回值格式化为string类型，DatetimePicker的值默认是date类型 | Boolean | true |
|vanFieldProps |Field组件（除了 readonly、clickable、is-link、label、placeholder）的所有属性的对象集合 | object | {} |

<br/>
