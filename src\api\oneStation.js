import http from '../utils/http';

export default {
  // 一站式重疾提交申请
  apply: (params, config) => http.post('/api-member/api/v1/oneStation/apply', params, config),
  // 是否已经有申请
  check: (params, config) => http.get('/api-member/api/v1/oneStation/check', params, config),
  // 查询申请列表
  queryList: (params, config) => http.post('/api-member/api/v1/oneStation/list', params, config),
  // 提交查询申请
  queryListRequired: (params, config) => http.post('/api-member/api/v1/oneStation/query', params, config),
}