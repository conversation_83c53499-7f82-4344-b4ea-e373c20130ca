const router = [{
  name: 'health-ah2024-activation',
  path: '/health-ah2024-activation',
  component: () => import( /* webpackChunkName: "health-ah2024-activation" */ '@/pages/ah2024/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-ah2024-origin',
  path: '/health-ah2024-origin',
  component: () => import( /* webpackChunkName: "health-ah2024-origin" */ '@/pages/ah2024/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-ah2024-introduction',
  path: '/health-ah2024-introduction',
  component: () => import( /* webpackChunkName: "health-ah2024-introduction" */ '@/pages/ah2024/introduction'),
  meta: {
    title: '权益介绍',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-ah2024-pharmacy',
  path: '/health-ah2024-pharmacy',
  component: () => import( /* webpackChunkName: "health-ah2024-pharmacy" */ '@/pages/ah2024/pharmacy'),
  meta: {
    title: '药房清单',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-ah2024-coupon',
  path: '/health-ah2024-coupon',
  component: () => import( /* webpackChunkName: "health-ah2024-coupon" */ '@/pages/ah2024/coupon'),
  meta: {
    title: '我的优惠券',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-ah2024-couponDetail',
  path: '/health-ah2024-couponDetail',
  component: () => import( /* webpackChunkName: "health-ah2024-couponDetail" */ '@/pages/ah2024/couponDetail'),
  meta: {
    title: '优惠券',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}];
export default router;