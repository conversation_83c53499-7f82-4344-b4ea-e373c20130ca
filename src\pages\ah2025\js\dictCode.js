export const productObj = {
  455: '老乡保',
}

export const idTypeList_normal = [
  {
    name: "身份证",
    value: 1
  }
]

export const idTypeList = [
  {
    name: "身份证",
    value: 1
  }, {
    name: "护照",
    value: 2
  }, {
    name: "港澳居民来往内地通行证",
    value: 3
  }, {
    name: "台湾居民来往大陆通行证",
    value: 30
  }, 
]

export const idTypeMoreList = [
  {
    name: "身份证",
    value: 1
  }, {
    name: "护照",
    value: 2
  }, {
    name: "军官证",
    value: 6
  }, {
    name: "外国人永久居留证",
    value: 9
  }, {
    name: "港澳居民来往内地通行证",
    value: 3
  }, {
    name: "台湾居民来往大陆通行证",
    value: 4
  }, {
    name: "出生证",
    value: 28
  }, {
    name: "居民户口薄",
    value: 30
  }, {
    name: "其他身份证件",
    value: 20
  },
]

// 证件类型全量
export const idTypeAllList = [
  {
    name: "身份证",
    value: 1
  }, {
    name: "护照",
    value: 2
  }, {
    name: "港澳居民来往内地通行证",
    value: 3
  }, {
    name: "台胞证",
    value: 4
  }, {
    name: "回乡证",
    value: 5
  }, {
    name: "军人身份证",
    value: 6
  }, {
    name: "港澳居民居住证",
    value: 7
  }, {
    name: "台湾居民居住证",
    value: 8
  }, {
    name: "居民户口薄",
    value: 9
  }, {
    name: "驾驶证",
    value: 13
  }, {
    name: "士兵证",
    value: 15
  }, {
    name: "军官离退休证",
    value: 16
  }, {
    name: "学生证",
    value: 18
  }, {
    name: "学生工作证",
    value: 19
  }, {
    name: "警官证",
    value: 22
  }, {
    name: "外国护照",
    value: 24
  }, {
    name: "旅行证",
    value: 25
  }, {
    name: "居留证件",
    value: 27
  }, {
    name: "台湾居民来往大陆通行证",
    value: 30
  }, {
    name: "外国人永久居留身份证",
    value: 31
  }, {
    name: "军官证",
    value: 32
  }, {
    name: "港澳通行证",
    value: 33
  }, {
    name: "出生证",
    value: 28
  }, {
    name: "港澳台居民居住证",
    value: 35
  }, {
    name: "社会保障卡",
    value: 36
  }, {
    name: "其他身份证件",
    value: 99
  },
]

// 孝感证件类型
export const idTypeXgList = [
  {
    name: "居民身份证(户口簿)",
    value: 1
  }, {
    name: "中国人民解放军军官证",
    value: 6
  }, {
    name: "中国人民武装警察警官证",
    value: 22
  }, {
    name: "香港特区护照/身份证明",
    value: 3
  }, {
    name: "澳门特区护照/身份证明",
    value: 3
  }, {
    name: "台湾居民来往大陆通行证",
    value: 4
  }, {
    name: "外国人永久居留证",
    value: 9
  }, {
    name: "护照",
    value: 2
  }, {
    name: "驾驶证",
    value: 13
  }, {
    name: "港澳居民来往内地通行证",
    value: 3
  }, {
    name: "港澳台居民居住证",
    value: 35
  }, {
    name: "出生医学证明",
    value: 34
  }, {
    name: "社会保障卡",
    value: 36
  }, {
    name: "其他身份证件",
    value: 99
  }
]

export const idTypeObj = {
  1: "身份证",
  2: "护照",
  6: "军官证",
  9: "外国人永久居留证",
  3: "港澳居民来往内地通行证",
  4: "台湾居民来往大陆通行证",
  28: "出生证",
  30: "居民户口薄",
  20: "其他身份证件",
}

export const sexList = [
  {
    name: "男",
    value: "m"
  }, {
    name: "女",
    value: "f"
  }
]

export const sexObj = {
  "m": "男",
  "f": "女"
}

export const relationObj = {
  '11': '本人',
  '13': '夫妻',
  '12': '父母',
  '14': '子女',
  '15': '兄弟姐妹',
  '99': '其他' // 该项通过配置添加
}