<template>
  <div class="introTRT">
    <img src="~images/atb/intro-banner.png" width="100%" />
    <img src="~images/atb/intro-middle.png" style="width: 3.59rem;margin-top: .12rem" />
    <img src="~images/atb/intro-callCenter.png" style="width: 3.43rem;margin: .22rem .16rem">

    <div class="btn" style="left: 0.41rem;" @click.capture="getPvUv('PR011-2')">
      <wx-open-launch-weapp id="launch-btn" :username="miniId" path="pages/user/coupondetail?act_id=9&source=1" style="width: .8rem">
        <script type="text/wxtag-template">
          <style>
            .btn {
              width: 100%;
              height: 32px;
              border-radius: 18px;
              background: linear-gradient(360deg, #FDA450 0%, #FDF5E3 99%, #FDF6E4 100%);
              box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.04);
              margin: 0;
              padding: 0;
              border: none;
              color: #EE5577;
              font-size: 14px;
              font-weight: 500;
              animation: scaleDraw 3s ease-in-out infinite;
              -webkit-animation: scaleDraw 3s ease-in-out infinite;
            }
            @keyframes scaleDraw {  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
              0%{
                transform: scale(1);  /*开始为原始大小*/
              }
              25%{
                transform: scale(0.9); /*放大1.1倍*/
              }
              50%{
                transform: scale(1);
              }
              75%{
                transform: scale(0.9);
              }
            }
          </style>
          <button class="btn">立即领取</button>
        </script>
      </wx-open-launch-weapp>
    </div>
    <div class="btn" style="left: 1.48rem;" @click.capture="getPvUv('PR011-1')">
      <wx-open-launch-weapp id="launch-btn" :username="miniId" path="pages/user/coupondetail?act_id=8&source=1" style="width: .8rem">
        <script type="text/wxtag-template">
          <style>
            .btn {
              width: 100%;
              height: 32px;
              border-radius: 18px;
              background: linear-gradient(360deg, #FDA450 0%, #FDF5E3 99%, #FDF6E4 100%);
              box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.04);
              margin: 0;
              padding: 0;
              border: none;
              color: #EE5577;
              font-size: 14px;
              font-weight: 500;
              animation: scaleDraw 3s ease-in-out infinite;
              -webkit-animation: scaleDraw 3s ease-in-out infinite;
            }
            @keyframes scaleDraw {  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
              0%{
                transform: scale(1);  /*开始为原始大小*/
              }
              25%{
                transform: scale(0.9); /*放大1.1倍*/
              }
              50%{
                transform: scale(1);
              }
              75%{
                transform: scale(0.9);
              }
            }
          </style>
          <button class="btn">立即领取</button>
        </script>
      </wx-open-launch-weapp>
    </div>
    <div class="btn" style="left: 2.54rem;" @click.capture="getPvUv('PR011')">
      <wx-open-launch-weapp id="launch-btn" :username="miniId" path="pages/user/coupondetail?act_id=12&source=1" style="width: .8rem">
        <script type="text/wxtag-template">
          <style>
            .btn {
              width: 100%;
              height: 32px;
              border-radius: 18px;
              background: linear-gradient(360deg, #FDA450 0%, #FDF5E3 99%, #FDF6E4 100%);
              box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.04);
              margin: 0;
              padding: 0;
              border: none;
              color: #EE5577;
              font-size: 14px;
              font-weight: 500;
              animation: scaleDraw 3s ease-in-out infinite;
              -webkit-animation: scaleDraw 3s ease-in-out infinite;
            }
            @keyframes scaleDraw {  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
              0%{
                transform: scale(1);  /*开始为原始大小*/
              }
              25%{
                transform: scale(0.9); /*放大1.1倍*/
              }
              50%{
                transform: scale(1);
              }
              75%{
                transform: scale(0.9);
              }
            }
          </style>
          <button class="btn">立即领取</button>
        </script>
      </wx-open-launch-weapp>
    </div>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import api_global from '@/api/global'
import wx from 'weixin-js-sdk'
export default {
  components: {},
  data() {
    return {
      cardNo: this.$route.query.cardNo || "",
      miniId: "gh_9e8d2c25cae5",
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.signature()
  },
  methods: {
    // 埋点
    getPvUv(val) {
      if (navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1) {
        api.getPvUv({ eventOwner: '103', eventCode: val, eventType: 'RIGHTS_URL'}).then(() => {})
      }
    },
    // 微信菜单
    signature() {
      const currentUrl = window.location.href.split('#')[0]

      const baseUrl = window.location.href.split(/\?|#/)[0]
      api_global.signature({ url: currentUrl }).then((res) => {
        const configInfo = (res && res.result) || {}
        wx.config({
          debug: false,
          appId: configInfo.appId,
          timestamp: configInfo.timestamp.toString(),
          nonceStr: configInfo.nonceStr,
          signature: configInfo.signature,
          jsApiList: ['wx-open-launch-weapp', 'updateAppMessageShareData', 'updateTimelineShareData'],
          openTagList: ['wx-open-launch-weapp'],
        })

        wx.ready(() => {
          wx.updateAppMessageShareData({
            title: '国风养生！领取北京同仁堂专属大额优惠，更有滋补佳品3.8折起 ', // 分享标题
            desc: '即刻领取优惠', // 分享描述
            imgUrl: baseUrl + 'atb-share.png', // 分享图标
            link: window.location.href, // 分享链接
            success() {}
          })
          wx.updateTimelineShareData({
            title: '国风养生！领取北京同仁堂专属大额优惠，更有滋补佳品3.8折起 ', // 分享标题
            desc: '即刻领取优惠', // 分享描述
            imgUrl: baseUrl + 'atb-share.png', // 分享图标
            link: window.location.href, // 分享链接
            success() {}
          })
        })
        wx.error((res) => {
          console.log('jssdk error')
          console.log(res)
        })
      })
    },
  },
}
</script>
<style lang="less" scoped>
.introTRT {
  width: 3.75rem;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
  background: url('../../assets/images/atb/shadow.png'), linear-gradient(180deg, #e04256 0%, #f34168 100%);
  background-repeat: no-repeat;
  background-size: 100%;
  .btn {
    width: .8rem;
    height: .32rem;
    background: transparent;
    padding: 0;
    border: none;
    position: absolute;
    top: 9.48rem;
    z-index: 100;
  }
}
</style>