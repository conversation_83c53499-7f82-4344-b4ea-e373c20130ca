const router = [{
  name: 'health-bh2024-activation',
  path: '/health-bh2024-activation',
  component: () => import( /* webpackChunkName: "health-bh2024-activation" */ '@/pages/bh2024/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-bh2024-origin',
  path: '/health-bh2024-origin',
  component: () => import( /* webpackChunkName: "health-bh2024-origin" */ '@/pages/bh2024/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-bh2024-introduction',
  path: '/health-bh2024-introduction',
  component: () => import( /* webpackChunkName: "health-bh2024-introduction" */ '@/pages/bh2024/introduction'),
  meta: {
    title: '权益介绍',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
},];
export default router;