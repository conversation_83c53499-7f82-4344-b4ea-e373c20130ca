<template>
  <div class="activation">
    <img src="~images/ah2024/banner.png" width="100%" />
    <div class="form_block">
      <img src="~images/ah2024/service1.png" style="width: 3.49rem;display: block;margin: 0 auto">
      <p class="title">绑定权益人</p>
      <van-form ref="activeInfo">
        <van-field
          v-model="params.bindName"
          placeholder="请输入姓名"
          clearable
          :rules="validateRules.nameRules"
        />
        <cx-field-select
          placeholder="请选择证件类型"
          optionsTitle="请选择证件类型"
          v-model="params.bindIdType"
          :options="idTypeList"
          :rules="validateRules.idTypeRules"
        />
        <van-field
          v-model="params.bindIdNum"
          placeholder="请输入证件号码"
          clearable
          :rules="idNumRules"
        />
      </van-form>
    </div>

    <!-- 激活按钮 -->
    <div class="footer">
      <div class="module">
        <read-agreement :confirmRead.sync="confirmRead" :agreementQuery="agreeUrlQuery" color="#FD5710"/>
      </div>
      <van-button :disabled="!completed" class="btn" @click="agreeCheck()">激活权益</van-button>
    </div>

    <img v-show="circleShow" src="~/images/hzqjf2024/home.png" class="side-btn" @click="toPage('/health-ah2024-origin')" >

    <!-- 协议弹窗 -->
    <van-popup v-model="agreementShow" class="dialog" position="bottom" closeable>
      <p class="dialog_title">服务协议及隐私保护</p>
      <div class="dialog_agreement">
        <span>为了更好的保护您的合法权益，请您阅读并同意以下协议</span>
        <zcx-agreement-box :isInline="true" :queryParams="agreeUrlQuery" color="#FD5710"/>
      </div>
      <div class="btn_list">
        <van-button class="unAgree" @click="agreementShow = false">不同意</van-button>
        <van-button class="agree" @click="active()">同意</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { theme } from '@/mixin'
import { idTypeList } from './js/dictCode'
import { pipFormat } from '@/utils/tools'
import api from '@/api/cityHealth'
import api_user from '@/api/user'
import readAgreement from '@/pages/components/readAgreement'
import ZcxAgreementBox from '@/components/ZcxAgreementBox'
import validateRules from '@/utils/fieldValidateRules'
export default {
  mixins: [theme],
  components: { readAgreement, ZcxAgreementBox },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"364",keys:"sysm"},
      params: {
        cardTypeCode: '364',
        bindName: '',
        bindIdType: '1',
        bindIdNum: '',
      },
      circleShow: false,
      confirmRead: false, // 阅读协议
      agreementShow: false, // 协议弹窗
      idTypeList, // 证件类型
      validateRules, // 校验规则
    }
  },
  computed: {
    completed() {
      return this.params.bindName && this.params.bindIdNum
    },
    // 证件号校验规则
    idNumRules() {
      const idType = this.params.bindIdType.toString()
      if (idType === '1') {
        // 身份证校验规则
        return validateRules.idNumRules
      } else {
        // 港澳居民来往内地通行证
        return validateRules.otherIdNumRules
      }
    },
  },
  watch: {
    'params.bindIdNum': function (val) {
      this.params.bindIdNum = pipFormat(val)
    },
  },
  created() {
    this.registered()
  },
  mounted() {},
  methods: {
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList({ cartTypeCode: this.params.cardTypeCode }).then((res) => {
        this.circleShow = res.result.length > 0
      })
    },
    // 校验阅读协议
    agreeCheck() {
      this.$refs.activeInfo
        .validate()
        .then(() => {
          if (!this.confirmRead) {
            this.agreementShow = true
            return
          }
          this.active()
        })
        .catch((err) => {
          console.info('校验失败,具体内容如下：')
          console.table(err)
        })
    },
    // 激活
    active() {
      var arr = []
      arr[0] = this.params
      api.activeAh2024(arr, { codeAllPass: true }).then((res) => {
        if (res.code === '200000') {
          if (this.$route.query.toFrom == 'back') {
            this.$router.go(-1)
          } else {
            this.$toast('激活成功')
            setTimeout(() => {
              this.$router.push('/health-ah2024-origin')
            }, 1500)
          }
        } else {
          this.agreementShow = false
          this.$toast(res.message)
        }
      })
    },
    // 跳转到指定页面
    toPage(path) {
      this.$router.push({ path })
    },
    // 查询登录信息
    registered() {
      api_user
        .registered({
          jumpSource: '',
          routing: '',
          extraParams: '',
        })
        .then((res) => {
          if (!res.result.registed) {
            this.$router.push({
              path: '/login',
              query: {
                returnUrl: encodeURIComponent(location.href),
              },
            })
          } else {
            this.queryCardList()
          }
        })
    },
  },
}
</script>
<style lang="less" scoped>
.activation {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .form_block {
    flex: 1;
    overflow: auto;
    padding: 0.2rem 0 0;
    width: 100%;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 0.24rem 0.24rem 0 0;
    margin-top: -0.2rem;
    .title {
      font-size: 22px;
      color: #444444;
      line-height: 35px;
      background-image: linear-gradient(90deg, #FC9752 0%, #F75923 100%);
      -webkit-background-clip: text;
      color: transparent;
      -webkit-text-fill-color: transparent;
      text-align: center;
      padding: .15rem 0 .08rem;
      font-weight: bold;
    }
  }
  .footer {
    background: #fff;
    width: 100%;
    .module {
      display: flex;
      background: #fff;
      padding: 0.12rem 0.16rem;
      box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
    }
    .btn {
      width: 3.27rem;
      height: 0.48rem;
      background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
      box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
      border-radius: 0.28rem;
      border: none;
      padding: 0;
      margin: 0.16rem 0.24rem;
      color: #fff;
      font-size: 0.2rem;
      line-height: 0.32rem;
      font-weight: bold;
    }
  }

  .side-btn {
    position: fixed;
    z-index: 500;
    right: -.06rem;
    bottom: 1.21rem;
    width: .64rem;
    height: .66rem;
  }

  .dialog {
    width: 100%;
    padding-top: 0.24rem;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 0.24rem 0.24rem 0px 0px;
    .dialog_title {
      font-size: 0.18rem;
      font-weight: bold;
      line-height: 0.28rem;
      color: #222222;
      text-align: center;
    }
    .dialog_agreement {
      padding: 0.24rem 0.24rem 0.48rem;
    }
    .btn_list {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      border-top: 1px solid #f4f4f4;
      padding: 0.16rem 0;
      .unAgree {
        width: 1.09rem;
        height: 0.48rem;
        background: #F1F4F5;
        border-radius: 0.24rem;
        padding: 0;
        font-size: 0.2rem;
        line-height: 0.32rem;
        color: #444444;
        border: none;
      }
      .agree {
        width: 1.5rem;
        height: 0.48rem;
        color: #fff;
        border-radius: 0.24rem;
        padding: 0;
        font-size: 0.2rem;
        line-height: 0.32rem;
        margin-left: 0.12rem;
        margin-right: .2rem;
        background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
        box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
      }
    }
  }
  /deep/.van-form {
    .van-field {
      width: 3.11rem;
      height: 0.48rem;
      background: #FAFAFA;
      border-radius: 0.22rem;
      display: flex;
      align-items: center;
      padding: 0 0.06rem 0 0.24rem;
      margin: 0 auto 0.16rem;
    }
    .van-field__control {
      color: #222222;
      font-weight: bold;
    }
    .van-field__clear {
      right: .12rem
    }
    input::placeholder {
      color: #bbbbbb;
      font-size: 0.16rem;
      font-weight: 400;
    }
    .van-cell::after {
      border-bottom: none;
    }
    .van-cell__right-icon {
      right: .16rem
    }
  }
}
</style>
