<template>
  <div class="introduction">
    <p class="title_top">{{ rightDetail.rightName }}</p>
    <div class="main">
      <div class="main_box">
        <!-- 选择省市区 -->
        <div class="choose_box">
          <van-button @click="choose()" type="text" class="choose_btn" icon-position="right" icon="arrow" :style="{color: !bindCity ? '#FD5710' : '#000000'}">{{!bindCity ? '请选择' : bindCity}}</van-button>
          <van-button @click="showCity = true" v-show="bindCity" type="text" class="exchange_btn" icon-position="left" :icon="require('../../assets/images/ah2024/exchange.png')">切换</van-button>
        </div>
        <!-- 介绍 -->
        <div class="intro">
          <p class="intro_title">服务内容：</p>
          <p class="intro_label">活动期间内每月提供1张“满18减10优惠券”，共6张。</p>
        </div>
        <div class="intro">
          <p class="intro_title">服务范围：</p>
          <div class="intro_label">安徽省指定药房。<van-button :to="'/health-ah2024-pharmacy?rightCode=' + rightCode" class="list_btn" icon-position="right" icon="arrow">药房清单</van-button></div>
          <div class="brand_box">
            <p style="color: #666666">合作品牌：</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">国胜大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">安徽高济敬贤堂大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">安徽广济大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">池州国立大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">滁州华巨百姓缘大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">阜阳市第一大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">铜陵百草堂大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">芜湖高济仟芳佰济药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">芜湖中山大药房</p>
            <p style="color: #666666;font-size: .12rem;line-height: .18rem;margin-top: .04rem">宣城高济康宁大药房</p>
          </div>
        </div>
        <div class="intro">
          <p class="intro_title">使用条件：</p>
          <p class="intro_label">1.仅限惠民保参保用户本人使用；</p>
          <p class="intro_label">2.仅限线下药房购药时使用；</p>
          <p class="intro_label">3.每张优惠券仅限当月使用，过期作废；</p>
          <p class="intro_label">4.优惠券可用于购买药房内所有药械商品；</p>
          <p class="intro_label">5.满减后金额可使用医保个账支付（需要购买药品为医保品）；</p>
          <p class="intro_label">6.特价、绿价签药品不参与活动，不与药房会员折扣共享，不与门店其他优惠共享。</p>
          <p class="intro_label">*池州、铜陵药房消费不计入会员积分。</p>
        </div>
      </div>
    </div>
    <div class="footer">
      <van-button type="primary" class="btn" @click="getPvUv()">去使用优惠</van-button>
    </div>
    <van-popup v-model="showCity" position="bottom" class="dialog">
      <p class="title">请选择区域</p>
      <div class="dia_body">
        <p class="province">省： {{ province }}</p>
        <ah-field-area
          label="市：*区："
          placeholder="请选择"
          pompTitle="请选择"
          v-model="city"
        />
      </div>
      <van-button class="confirm_btn" @click="confirm()">确认</van-button>
    </van-popup>
    <van-popup v-model="showBrand" position="bottom" class="dialog">
      <p class="title">请选择品牌</p>
      <van-picker
        :columns="brandList"
        value-key="brand"
        :visible-item-count="brandList.length + 1"
        @change="onChange"
      />
      <van-button class="confirm_btn" @click="next()">确认</van-button>
    </van-popup>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import ahFieldArea from './components/fieldArea.vue'
export default {
  components: {ahFieldArea},
  data() {
    return {
      bindCity: "",
      showCity: false,
      showBrand: false,
      province: "安徽省",
      city: "",
      cardNo: this.$route.query.cardNo || "",
      rightCode: this.$route.query.rightCode || "",
      rightDetail: "",
      brandList: [],
      source: "",
    }
  },
  computed: {},
  watch: {},
  created() {
    this.queryCardList()
    this.getRights()
  },
  mounted() {},
  methods: {
    choose() {
      if(!this.bindCity) {
        this.showCity = true
        return
      }
    },
    confirm() {
      if(!this.city) {
        this.$toast('请先选择所在区域')
        return
      }
      api.getBrand({
        cardNo: this.cardNo,
        rightCode: this.rightCode,
        city: this.city
      }, { noLoading: true }).then((res) => {
        if(res.result.length == 0) {
          this.$toast('当前所选区域暂无品牌药房，请选择其他区域')
          return;
        }
        this.brandList = res.result
        this.showCity = false
        this.showBrand = true
        this.source = this.brandList[0].source
      })
    },
    onChange(picker, value, index) {
      console.log(picker,value, index)
      this.source = value.source
    },
    next() {
      // api.submitBrand({cardNo: this.cardNo, source: this.source}).then(() => {
        this.to()
      // })
    },
    queryCardList() {
      api.queryCardList({cardNo: this.cardNo}).then((res) => {
        this.bindCity = this.city = res.result[0].bindCity || ''
      })
    },
    getRights() {
      api.pbmCardRights({ cardNo: this.cardNo }).then((res) => {
        this.rightDetail = res.result.filter((item) => item.rightCode == this.rightCode)[0]
      })
    },
    getPvUv() {
      api.getPvUv({
        eventOwner: this.rightDetail.cardTypeCode,
        eventCode: this.rightCode,
        eventType: 'RIGHTS',
      })
      .then(() => {
        this.confirm()
      })
    },
    to() {
      this.$router.push({
        path: '/health-ah2024-coupon',
        query: {
          cardNo: this.cardNo,
          rightCode: this.rightCode,
          source: this.source
        },
      })
    },
  },
}
</script>
<style lang="less" scoped>
.introduction {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(360deg, #ffffff 0%, #fff9f6 100%);
  .title_top {
    font-size: 0.22rem;
    line-height: 0.3rem;
    font-family: 'ShuHei-B';
    color: #FD5710;
    padding: .2rem .12rem
  }
  .main {
    flex: 1;
    overflow: auto;
    padding-top: .12rem;
    width: 3.73rem;
    background: rgba(255,255,255,0.5);
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    border-radius: .24rem .24rem 0 0;
    border: 1px solid #FFFFFF;
    .main_box {
      margin: 0 .12rem .12rem;
      padding: .04rem 0 .08rem;
      background: #FFFFFF;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.06);
      border-radius: .16rem;
      .choose_box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: .16rem .16rem .12rem;
        border-bottom: 1px solid #F4F4F4;
        .choose_btn {
          width: auto;
          height: auto;
          padding: 0;
          border: none;
          background: transparent;
          font-size: .16rem;
          color: #FD5710;
          line-height: .24rem;
          font-weight: bold;
        }
        .exchange_btn {
          width: auto;
          height: auto;
          padding: 0;
          border: none;
          background: transparent;
          font-size: .14rem;
          color: #FD5710;
          line-height: .2rem;
        }
      }
      .intro {
        border-bottom: 1px solid #F4F4F4;
        margin: 0 .16rem;
        padding: .16rem 0;
        .circle {
          width: .06rem;
          height: .06rem;
          background: #FD5710;
          opacity: 0.8;
          border-radius: 50%;
        }
        .intro_title {
          font-size: .14rem;
          color: #222222;
          line-height: .2rem;
          display: flex;
          align-items: center;
        }
        .intro_title::before {
          content: "";
          position: relative;
          width: .06rem;
          height: .06rem;
          background: #FD5710;
          opacity: 0.8;
          border-radius: 50%;
          margin-right: .06rem;
        }
        .intro_label {
          font-size: .14rem;
          color: #666666;
          line-height: .21rem;
          margin-top: .06rem;
          padding-left: .12rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .list_btn {
            padding: 0;
            border: none;
            width: auto;
            height: auto;
            color: #FD5710;
            line-height: .21rem;
          }
          /deep/.van-button__icon + .van-button__text, .van-button__loading + .van-button__text, .van-button__text + .van-button__icon, .van-button__text + .van-button__loading {
            margin-left: 0;
          }
        }
        .brand_box {
          margin-top: .12rem;
          margin-left: .12rem;
          padding: .12rem;
          background: #FAFAFA;
          border-radius: .08rem;
        }
      }
      .intro:last-of-type {
        border-bottom: none
      }
    }
  }
  .footer {
    width: 100%;
    background: #ffffff;
    border-top: 1px solid #f4f4f4;
    .btn {
      width: 3.27rem;
      height: 0.48rem;
      background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
      box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
      border-radius: 0.28rem;
      color: #fff;
      font-size: 0.2rem;
      font-weight: bold;
      border: none;
      padding: 0 0;
      margin: 0.16rem auto;
      display: block;
    }
  }
  .dialog {
    width: 3.75rem;
    // height: 348px;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    border-radius: .24rem .24rem 0 0;
    .title {
      padding: .2rem 0 .24rem;
      text-align: center;
      font-weight: bold;
      font-size: .2rem;
      color: #222222;
      line-height: .32rem;
    }
    .dia_body {
      padding: 0 .24rem;
      .province {
        font-size: .16rem;
        color: #222222;
        line-height: .2rem;
      }
      /deep/.van-field {
        align-items: center;
        padding: .24rem 0 0;
      }
      /deep/.van-field .van-field__label {
        font-size: .16rem;
        color: #222222;
        line-height: .2rem;
        width: auto;
        margin-right: 0;
      }
      /deep/.van-field__value {
        flex: 1;
        padding: .14rem .24rem;
        background: #FAFAFA;
        border-radius: .24rem;
      }
      /deep/.van-cell::after {
        border: none
      }
      /deep/.van-field .van-cell__right-icon {
        bottom:.13rem;
      }
    }
    /deep/.van-picker-column__item {
      color: #666666;
    }
    /deep/.van-picker-column__item--selected {
      font-size: .18rem;
      font-weight: bold;
      line-height: .24rem;
      color: #222222;
    }
    /deep/.van-picker-column {
      position: relative;
      z-index: 10
    }
    /deep/.van-picker__frame {
      background: #F3F3F4;
      border-radius: .08rem;
    }
    .confirm_btn {
      width: 3.27rem;
      height: .48rem;
      background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
      box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
      border-radius: .28rem;
      padding: 0;
      border: none;
      font-size: .2rem;
      color: #FFFFFF;
      line-height: .32rem;
      font-weight: bold;
      margin: .36rem auto .24rem;
      display: block
    }
  }
}
</style>
