<template>
  <div class="origin">
    <!-- 顶部个人信息 -->
    <div class="top">
      <svg-icon class="icon_phone" iconName="city_phone" color="#FD5710" />
      <p class="phone">{{ currentCardInfo.bindPhoneNo }}</p>
      <van-button class="myCenter" @click="centerShow = true" :icon="require('../../assets/images/zy2024/right.png')" icon-position="right">我的</van-button>
    </div>
    <!-- 权益人： -->
    <div class="name_block">
      <div class="name_list">
        <p class="first_one">{{ currentCardInfo && currentCardInfo.bindName.split('')[0] }}</p>
        <div class="line"></div>
        <p class="other_one" @click="choose(item)" 
          v-for="(item, index) in otherList.slice(0, 5)" :key="index" 
          :style="{'margin-left': index == 0 ? '0' : '-.12rem', 'z-index': (otherList.length - index) * 10 }"
        >
          {{ item.bindName.split('')[0] }}
        </p>
      </div>
      <van-button class="add_change_btn" @click="holderShow = true;">新增/切换</van-button>
    </div>
    <!-- 卡信息 -->
    <div class="cardInfo_outside">
      <div class="cardInfo">
        <p class="endTime">有效期至：{{ currentCardInfo && currentCardInfo.endTime }}日</p>
        <div class="nameAndProd" v-show="currentCardInfo">
          <p>{{ currentCardInfo && currentCardInfo.bindName }}</p>
          <p class="productName">安徽惠民保购药优惠服务（2023）</p>
        </div>
        <p class="bindIdNum">{{ currentCardInfo && currentCardInfo.bindIdNum }}</p>
      </div>
    </div>
    <!-- 权益列表 -->
    <div class="rights_block">
      <img src="~images/zy2024/rights_block.png" width="100%" style="display: block;">
      <div style="background: #fff;width: 3.75rem">
        <div class="rights_box">
          <div class="rights_box_top"></div>
          <div class="rights_box_back"></div>
          <div class="rights_box_back_after"></div>
        </div>
        <div class="rights_box_main">
          <div class="title">权益展示</div>
          <van-button @click="agreeShow()" class="service_btn" icon="arrow" icon-position="right">服务手册</van-button>
          <van-empty
            v-if="rightsList.length === 0" 
            :image="require('../../assets/images/zy2024/empty.png')"
            :description="'服务已失效\n如有疑问请联系客服'" 
          />
          <div v-else>
            <div v-for="(m, n) in rightsList" :key="n">
              <div class="catName">
                <i class="front"></i>
                <span>{{m.name}}</span>
                <i class="back"></i>
              </div>
              <div class="rightInfo" v-for="(item, index) in m.list" :key="index">
                <p class="count" v-show="item.maxUse > 0">{{ item.maxUse - item.usedCount }}次</p>
                <div style="display: flex;align-items: flex-start;">
                  <img :src="item.icon" class="rightIcon" />
                  <div style="flex: 1; margin-top: 0.04rem;max-width: 1.68rem;padding-right: .12rem;">
                    <p class="rightName">{{ item.rightName }}</p>
                    <div v-if="item.rightTips">
                      <p v-for="(m, n) in item.rightTips.split(' ')" :key="n" class="tips">{{ m }}</p>
                    </div>
                    
                  </div>
                  <van-button class="rightBtn" @click="reserve(item)" :disabled="item.status == 'DISABLED' || item.disabled || waitStatus">{{ waitStatus || item.status == 'DISABLED' || item.disabled ? (item.noUsedCount ? '已用完' : '未生效') : '去使用'}}</van-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权益人选择器 -->
    <van-action-sheet
      v-model="holderShow"
      description="权益人"
      :closeable="true"
      :close-icon="require('../../assets/images/icon/close.png')"
    >
      <div class="content" :style="{ 'max-height': height + 'px' }">
        <van-radio-group v-model="actionCardInfo">
          <van-cell-group :border="false">
            <div v-for="(item, index) in cardList" :key="index">
              <van-cell clickable>
                <template #title>
                  <span>{{ item.bindName }}</span>
                </template>
                <template #label>
                  <img src="~images/icon/idNum.png" class="img-icon" />
                  <p>{{ item.bindIdNum }}</p>
                </template>
                <template #right-icon>
                  <van-radio :name="item" checked-color="#FD5710" @click="confirmAction">
                  </van-radio>
                </template>
              </van-cell>
            </div>
          </van-cell-group>
        </van-radio-group>
      </div>

      <div class="footer">
        <van-button type="primary" class="btn" :to="'/health-ah2024-activation'">新增</van-button>
      </div>
    </van-action-sheet>

    <!-- 个人中心 -->
    <van-popup v-model="centerShow" class="dialog" position="bottom" closeable>
      <p class="dialog_title">我的</p>
      <div class="dialog_detail">
        <img src="~images/hzqjf2024/my.png">
        <div style="flex: 1;">
          <p class="dialog_info">您好，<span>安徽惠民保购药优惠服务（2023）健管</span>用户</p>
          <p class="dialog_phone">{{ currentCardInfo.bindPhoneNo }}</p>
        </div>
      </div>
      <div class="login-box" @click="loginOut">
          <img src="~images/icon/loginOut.png">
          <p>退出登录</p>
        </div>
    </van-popup>

    <!-- 小程序 -->
    <van-popup v-model="miniShow" class="miniDialog">
      <p class="title">{{ miniName }}</p>
      <p class="text">赶紧去使用权益吧</p>
      <wx-open-launch-weapp id="launch-btn" :username="miniOrgId" :path="miniPath" style="margin: 0 auto;display: block;">
        <component :is="'script'" type="text/wxtag-template">
          <component :is="'style'">
            .btn {
              width: 263px;
              height: 48px;
              border-radius: 24px;
              margin: 24px auto 24px;
              background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
              box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
              border: none;
              color: #fff;
              font-size: 16px;
              display: block;
            }
          </component>
          <button class="btn">去{{ miniName }}领取权益</button>
        </component>
      </wx-open-launch-weapp>
    </van-popup>

    <!-- 协议弹窗 -->
    <zcx-agreement-popup v-model="agreePopup" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <iframe :src="currentComponent" width="100%" height="100%" frameborder="0"></iframe>
      </div>
    </zcx-agreement-popup>
  </div>
</template>
<script>
import wx from 'weixin-js-sdk'
import api from '@/api/cityHealth'
import api_user from '@/api/user'
import api_global from '@/api/global'
import { maskString, groupArr, interchangeZh } from '@/utils/tools'
import ZcxAgreementPopup from '@/components/ZcxAgreementPopup'
export default {
  components: { ZcxAgreementPopup },
  data() {
    return {
      cardList: [],
      otherList: [],
      currentCardInfo: "",
      actionCardInfo: "",
      rightsList: [],
      activeNames: [],
      holderShow: false, // 选择权益人弹窗
      centerShow: false,
      height: document.documentElement.clientHeight * 0.7,
      miniName: '', // 小程序名称
      miniOrgId: '', // 小程序id
      miniPath: '', // 小程序路径
      miniShow: false, // 小程序弹窗
      agreePopup: false,
      currentName: '', // 协议名字
      currentComponent: '', // 协议内容
    }
  },
  computed: {
    waitStatus() {
      if (this.currentCardInfo.needWait && this.currentCardInfo.status == 'AVILABLE') {
        return true
      } else {
        return false
      }
    }
  },
  watch: {},
  created() {
    this.signature()
    this.queryCardList()
  },
  mounted() {},
  methods: {
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList().then((res) => {
        var arr = res.result.filter(item => item.cardTypeCode == '364')
        console.log('arr', arr)
        if (arr.length == 0) {
          this.$router.push({
            path: '/health-ah2024-activation',
          })
          return
        }
        arr.forEach((ele) => {
          ele.bindPhoneNo = maskString(ele.bindPhoneNo, 3, 4)
          ele.bindIdNum = maskString(ele.bindIdNum, 3, 4)
        })
        this.cardList = arr
        if(localStorage.getItem('health_ah2024_cardInfo')) {
          var obj = JSON.parse(localStorage.getItem('health_ah2024_cardInfo'))
          this.currentCardInfo = this.actionCardInfo = this.cardList.filter(item => item.cardNo == obj.cardNo)[0]
          console.log('currentCardInfo',this.currentCardInfo)
        } else {
          this.currentCardInfo = this.actionCardInfo = this.cardList[0]
          console.log('currentCardInfo',this.currentCardInfo)
        }
        this.currentCardInfo.endTime = interchangeZh(this.currentCardInfo.endTime)
        this.otherList = this.cardList.filter(item => item.cardNo != this.currentCardInfo.cardNo)
        this.queryRights()
      })
    },
    // 查询权益
    queryRights() {
      api.pbmCardRights({ cardNo: this.currentCardInfo.cardNo }, { noLoading: true })
        .then((res) => {
          res.result.forEach((ele) => {
            ele.startTime = interchangeZh(ele.startTime)
            ele.endTime = interchangeZh(ele.endTime)
            ele.url = ele.url ? JSON.parse(ele.url) : ele.url
            if(ele.status != 'DISABLED' && ele.status != 'EXPR') {
              if(ele.maxUse > 0 && ele.maxUse <= ele.usedCount || this.currentCardInfo.freeCount == 0) {
                ele.disabled = true
                ele.noUsedCount = true
              } else {
                ele.disabled = false
              }
            } else {
              ele.disabled = true
            }
          })
          if(this.currentCardInfo.status == 'UNAVILABLE') {
            this.rightsList = []
            this.activeNames = []
          } else {
            this.rightsList = groupArr(res.result, 'catName')
            this.activeNames = Array.from({ length: this.rightsList.length }).map((item, index) => {
              return index
            })
          }
        })
    },
    // 确认选择当前权益人
    confirmAction() {
      this.currentCardInfo = this.actionCardInfo
      this.currentCardInfo.endTime = interchangeZh(this.currentCardInfo.endTime)
      localStorage.setItem('health_ah2024_cardInfo', JSON.stringify(this.actionCardInfo))
      this.otherList = this.cardList.filter(item => item.cardNo != this.currentCardInfo.cardNo)
      this.queryRights()
      this.holderShow = false
    },
    // 点头像选择
    choose(item) {
      this.currentCardInfo = this.actionCardInfo = item
      this.currentCardInfo.endTime = interchangeZh(this.currentCardInfo.endTime)
      localStorage.setItem('health_ah2024_cardInfo', JSON.stringify(this.actionCardInfo))
      this.otherList = this.cardList.filter(item => item.cardNo != this.currentCardInfo.cardNo)
      this.queryRights()
    },
    // 预约权益
    reserve(event) {
      if (event.url && event.url.type == 'url' && event.rightCode != 'PR932') {
        location.href = event.url.path
          .replace('{cardNo}', event.cardNo)
          .replace('{idNum}', this.currentCardInfo.bindIdNum)
          .replace('{cardType}', this.currentCardInfo.cardTypeCode)
          .replace('{rightCode}', event.rightCode)
          .replace('{userId}', this.currentCardInfo.userId)
      } else if(event.url && event.url.type == 'miniApp') {
        this.miniName = event.url.name;
        this.miniOrgId = event.url.orgId;
        this.miniPath = event.url.path;
        this.miniShow = true
      } else {
        this.$router.push({
          path: '/health-ah2024-introduction',
          query: {
            cardNo: event.cardNo,
            rightCode: event.rightCode,
          },
        })
      }
    },
    agreeShow() {
      api.queryAgreementList('?cardTypeCode=364&keys=sysm').then((res) => {
        if(res.result){
          this.currentName = res.result[0].name
          this.currentComponent = res.result[0].url
        }
        console.log(this.currentName, this.currentComponent)
        this.agreePopup = true
      })
    },
    // 登出账户
    loginOut() {
      api_user.loginOut().then(() => {
        localStorage.removeItem('health_ah2024_cardInfo')
        this.registered()
      })
    },
    // 微信菜单
    signature() {
      const currentUrl = window.location.href.split('#')[0]
      api_global.signature({ url: currentUrl }).then((res) => {
        const configInfo = (res && res.result) || {}
        wx.config({
          debug: false,
          appId: configInfo.appId,
          timestamp: configInfo.timestamp.toString(),
          nonceStr: configInfo.nonceStr,
          signature: configInfo.signature,
          jsApiList: ['wx-open-launch-weapp'],
          openTagList: ['wx-open-launch-weapp'],
        })

        wx.ready((res) => {
          console.log('ready:', res)
        })
        wx.error((res) => {
          console.log('jssdk error')
          console.log(res)
        })
      })
    },

    // 查询登录信息
    registered() {
      api_user
        .registered({
          jumpSource: '',
          routing: '',
          extraParams: '',
        })
        .then((res) => {
          if (!res.result.registed) {
            this.$router.push({
              path: '/login',
              query: {
                returnUrl: encodeURIComponent(location.href),
              },
            })
            return
          }
        })
    },
  },
}
</script>
<style lang="less" scoped>
.origin {
  height: 100vh;
  overflow: auto;
  background: linear-gradient( 360deg, #FFFFFF 0%, #FFF9F6 100%);
  .top {
    display: flex;
    align-items: center;
    padding: .08rem .12rem;
    background: #fff;
    .icon_phone {
      width: .2rem;
      height: .2rem;
    }
    .phone {
      font-size: .14rem;
      color: #222222;
      line-height: .2rem;
      margin-left: .06rem;
      flex: 1;
    }
    .myCenter {
      width: .5rem;
      height: .2rem;
      border: none;
      padding: 0;
      font-size: .14rem;
      color: #666666;
      line-height: .2rem;
      display: flex;
      align-items: center;
      .van-icon__image {
        width: .16rem;
        height: .16rem;
      }
    }
  }
  .name_block {
    padding: .12rem .2rem .1rem;
    display: flex;
    align-items: center;
    .name_list {
      flex: 1;
      margin-right: .2rem;
      display: flex;
      align-items: center;
      .first_one {
        width: .48rem;
        height: .48rem;
        background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
        border: 1px solid #FEE9D8;
        border-radius: 50%;
        font-size: .18rem;
        color: #FFFFFF;
        line-height: .48rem;
        text-align: center;
      }
      .line {
        width: .01rem;
        height: .24rem;
        background: #DDDDDD;
        margin: 0 .08rem;
      }
      .other_one {
        width: .4rem;
        height: .4rem;
        background: linear-gradient( 135deg, #F1F4F5 0%, #C6CCD6 100%);
        border: 1px solid #FFFFFF;
        border-radius: 50%; 
        font-size: .14rem;
        color: #FFFFFF;
        line-height: .4rem;
        text-align: center;
        position: relative;
      }
    }
    .add_change_btn {
      width: .86rem;
      height: .32rem;
      background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
      box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
      border-radius: .28rem;
      font-size: .14rem;
      line-height: .24rem;
      color: #FFFFFF;
      padding: 0;
      border: none;
      font-family: 'OPPOSans-B';
    }
  }
  .cardInfo_outside {
    background: linear-gradient( 153deg, #FEECD5 0%, #F8D5AE 100%);width: 3.51rem;
    height: 1.2rem;
    border-radius: .16rem .16rem 0 0;
    box-shadow: 0px 2px 4px 0px rgba(248,213,174,0.2);
    margin: 0 auto;
    position: relative;
  }
  .cardInfo {
    width: 3.51rem;
    height: 1.2rem;
    border-radius: .16rem .16rem 0 0;
    background: url('../../assets/images/zy2024/cardInfo_bg.png') top 0.32rem right .2rem;
    background-color: transparent;
    background-repeat: no-repeat;
    background-size: 1rem .92rem;
    box-shadow: 0px 2px 4px 0px rgba(248,213,174,0.2);
    margin: 0 auto;
    position: relative;
    .endTime {
      background: rgba(255, 255, 255, .4);
      border-radius: 0 .1rem 0 .1rem;
      position: absolute;
      right:0;
      top:0;
      font-family: 'OPPOSans-R';
      font-size: .12rem;
      color: #6E2F1D;
      line-height: .16rem;
      text-align: center;
      padding: .02rem .12rem;
    }
    .nameAndProd {
      font-size: .18rem;
      color: #6E2F1D;
      line-height: .24rem;
      padding: .24rem .16rem .08rem;
      display: flex;
      align-items: center;     
      .productName {
        padding: .02rem .04rem;
        background: linear-gradient( 315deg, #FFEACE 0%, #FEF4ED 100%);
        border: 1px solid #FEE9D8;
        font-size: .12rem;
        color: #C07F31;
        line-height: .16rem;
        margin-left: .08rem;
        position: relative;
      }
      .productName::before {
        content: "";
        width: 0;
        height: 0;
        border-top: 0px solid transparent;
        border-bottom: .2rem solid #FEF2E8;
        border-left: .04rem solid transparent;
        border-right: 0px solid transparent;
        position: absolute;
        left: -.04rem;
        top: 0;
      }
      .productName::after {
        content: "";
        width: 0;
        height: 0;
        border-top: 0.22rem solid #FFEBD1;
        border-bottom: 0.2rem solid transparent;
        border-left: 0px solid transparent;
        border-right: .04rem solid transparent;
        position: absolute;
        right: -0.04rem;
        top: 0;
      }
    }
    .bindIdNum {
      font-size: .14rem;
      color: #6E2F1D;
      line-height: .2rem;     
      padding: 0 .16rem;
    }
  }
  .cardInfo::before {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border-left: .06rem solid transparent;
    border-top: .06rem solid transparent;
    border-right: .06rem solid transparent;
    border-bottom: .06rem solid #FDE9CF;
    top: -0.12rem;
    left: .24rem;
  }
  .rights_block {
    min-height: 1rem;
    margin-top: -.32rem;
    position: relative;
    .rights_box {
      position: relative;
      width: 3.51rem;
      height: .28rem;
      overflow: hidden;
      box-sizing: border-box;
      margin: 0 auto;
      border-radius: .16rem 0 0 0;
      .rights_box_top {
        position: relative;
        width: 1.2rem;
        height: 0.16rem;
        background: #FFFFFF;
        bottom: 0;
        border-radius: 16px 6px 0 0;
        transform: perspective(40px) scaleX(1) scaleY(1) rotateX(20deg) translate(-10px, 0);
        transform-origin: 50% 100%;
        z-index: 2;
        display: block;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06), inset 6px 2px 0px 0px rgba(28, 53, 94, 0.02)
      }
      .rights_box_top::before {
        content: "";
        position: absolute;
        right: -0.1rem;
        width: .1rem;
        height: .1rem;
        top: .06rem;
        background: radial-gradient(circle at 100% 0, transparent, transparent .095rem, #FFF9F6 .1rem, #FFF9F6);
      }
      .rights_box_back {
        position: absolute;
        left: .2rem;
        width: 1rem;
        height: 0.1rem;
        background: linear-gradient( 270deg, #F4ABA5 0%, #F6BBAD 100%);
        top: .014rem;
        border-radius: 15px 8px 0 0;
        transform: perspective(25px) scaleX(1) scaleY(1) rotateX(20deg) translate(-10px, 0);
        transform-origin: 50% 100%;
        z-index: 1;
        display: block;
      }
      .rights_box_back::before {
        content: "";
        position: absolute;
        z-index: 1;
        right: -8.78px;
        width: 10px;
        height: 8.6px;
        top: 2px;
        background: radial-gradient(circle at 100% -1px, transparent, transparent 0.095rem, #F0A9A4 10px, #F0A9A4);
      }
      .rights_box_back_after {
        position: absolute;
        top: .12rem;
        right: 0;
        width: 3rem;
        height: .5rem;
        background: linear-gradient( 270deg, #FF7F5C 0%, #E54441 100%);
        opacity: 0.5;
        border-top-right-radius: .16rem;
        z-index: 1;
      }
    }
    .rights_box_main{
      padding: 0 .16rem .16rem;
      background: #fff;
      border-radius: 0 16px 16px 16px;
      margin: -.12rem .12rem 0;
      position: relative;
      z-index: 2;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06), inset 0px 0px 0px 0px rgba(28, 53, 94, 0.12);
      .title {
        font-size: 0.22rem;
        line-height: 0.3rem;
        display: flex;
        color: #222222;
        font-family: 'ShuHei-B';
      }
      .service_btn {
        border: none;
        padding: 0;
        color: #6E2F1D;
        font-size: .14rem;
        line-height: .18rem;
        position: absolute;
        right: .12rem;
        top: .12rem;
        height: auto;
        width: auto;
      }
      .catName {
        font-family: 'OPPOSans-B';
        font-size: .18rem;
        color: #E39E51;
        line-height: .24rem;
        text-align: center;
        margin: .12rem auto;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        .front {
          margin-right: 0.12rem;
          width: 74px;
          height: 2px;
          background: linear-gradient( 90deg, #FFFFFF 0%, #F7C89A 100%);
        }
        .back {
          margin-left: 0.12rem;
          width: 74px;
          height: 2px;
          background: linear-gradient( 270deg, #FFFFFF 0%, #F7C89A 100%);
        }
      }
      .rightInfo {
        padding: 0;
        margin: 0 auto 0.2rem;
        position: relative;
        .count {
          width: 0.28rem;
          height: 0.14rem;
          background: #F51A1A;
          color: #fff;
          font-size: 0.1rem;
          line-height: 0.14rem;
          text-align: center;
          border-radius: .07rem .07rem .07rem 0;
          position: absolute;
          left: .28rem;
          top: -.06rem;
        }
        .rightIcon {
          width: 0.56rem;
          height: 0.56rem;
          margin-right: 0.08rem;
        }
        .rightName {
          line-height: 0.2rem;
          color: #222222;         
          font-size: 0.14rem;
          white-space: pre-line;
          // text-overflow: ellipsis;
          // overflow: hidden;
          display: flex;
          align-items: center;
        }
        .tips {
          background: #FFF9F2;
          border-radius: 0.04rem;
          color: #C07F31;
          line-height: 0.16rem;
          padding: 0.02rem 0.04rem;
          margin-right: 0.04rem;
          display: inline-block;
          vertical-align: middle;
          margin-top: 0.08rem;
          font-size: 0.12rem;
        }
        .endTime {
          font-size: 0.12rem;
          color: #666666;
          line-height: 0.16rem;
          padding: 0.04rem 0 0 0.64rem;
        }
        .rightBtn {
          width: 0.74rem;
          height: 0.32rem;
          background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
          box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
          border-radius: 0.28rem;
          padding: 0;
          border: none;
          color: #fff;
          font-family: 'OPPOSans-B';
          margin-top: 0.12rem;
        }
      }
    }
  }

  .content {
    .van-cell-group {
      padding: 0 0.16rem;
      padding-bottom: 0.8rem;
    }
    .van-cell {
      background: #fafafa;
      border-radius: 0.12rem;
      overflow: hidden;
      margin-bottom: 0.12rem;
      padding: 0.12rem 0.16rem;
    }
    .van-cell__title,
    .van-cell__value {
      font-size: 0.14rem;
      font-weight: 500;
      color: #222222;
      line-height: 0.2rem;
    }
    .van-cell__label {
      font-size: 0.12rem;
      color: #666666;
      font-weight: 400;
      line-height: 0.16rem;
      display: flex;
      align-items: center;
      margin-top: 0.08rem;
    }
    .img-icon {
      width: 0.16rem;
      margin-right: 0.04rem;
    }
  }

  .footer {
    width: 100%;
    height: 0.8rem;
    border-top: 1px solid #f4f4f4;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
      box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
      border: none;
    }
  }
  /deep/.van-action-sheet__description {
    font-size: 0.2rem;
    line-height: 0.28rem;
    font-weight: 500;
    color: #222222;
    padding: 0.2rem 0;
  }
  /deep/.van-action-sheet__description::after {
    border-bottom: none;
  }
  .dialog {
    width: 100%;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 0.24rem 0.24rem 0px 0px;
    .dialog_title {
      font-size: 0.2rem;
      font-family: 'OPPOSans-B';
      line-height: 0.32rem;
      color: #222222;
      text-align: center;
      margin-top: .2rem;
    }
    .dialog_detail {
      display: flex;
      align-items: center;
      padding: .16rem;
      img {
        width: .67rem;
        height: .67rem;
        margin-right: .06rem;
      }
      .dialog_info {
        font-size: .16rem;
        color: #222222;
        line-height: .24rem;
        span {
          color: #FD5710;
        }
      }
      .dialog_phone {
        font-size: .14rem;
        color: #222222;
        line-height: .2rem;
        padding-top: .04rem;
      }
    }
    .login-box {
      margin: .64rem auto .32rem;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: .24rem;
        height: .24rem
      }
      p{
        color: #CCCCCC;
        font-weight: 500;
        margin-left: .08rem
      }
    }
    /deep/.van-popup__close-icon--top-right {
      top: .28rem;
    }
    /deep/.van-cell-group {
      margin: 0.2rem auto 0;
      display: block;
      width: 3.43rem;
      border-radius: .12rem;
      border: 1px solid #EEEEEE;
      overflow: hidden;
      background: #fff;
      .van-cell {
        padding: .18rem .16rem;
        align-items: center;
      }
      .van-cell__left-icon {
        margin-right: .12rem;
        width: .28rem;
        height: .28rem;
        img {
          width: 100%;
          height: 100%
        }
      }
      .van-cell__title, .van-cell__value {
        font-size: .16rem;
        color: #222222
      }
    }
    /deep/.van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border: none
    }
  }
  .miniDialog {
    width: 3.11rem;
    background: #ffffff;
    border-radius: 0.16rem;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    .title {
      margin: 0.24rem auto 0.16rem;
      text-align: center;
      font-size: 0.2rem;
      font-weight: 500;
      line-height: 0.28rem;
    }
    .text {
      font-size: 0.16rem;
      line-height: 0.24rem;
      padding: 0 0.24rem 0.24rem;
      text-align: center;
    }
  }
  .readPopup {
    height: 100%;
    display: flex;
    flex-direction: column;
    .title {
      height: 0.6rem;
      box-sizing: border-box;
      line-height: 0.28rem;
      padding: 0.16rem;
      text-align: center;
      font-weight: 400;
      color: #333333;
      font-size: 0.16rem;
      position: relative;

      h2 {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
    }
    .agreementBox {
      flex: 1;
      overflow-y: scroll;
      margin-bottom: 0.1rem;
    }
  }
  /deep/.van-button--disabled {
    background: #8D9AAE !important;
    box-shadow: 0px 2px 8px 0px rgba(141,154,174,0.2), inset 0px 2px 4px 0px rgba(114,126,145,0.4), inset 0px -2px 4px 0px rgba(203,209,218,0.5) !important;
    opacity: 0.5;
  }
  /deep/.van-empty {
    margin-top: .4rem;
    .van-empty__image {
      width: 1.43rem;
      height: 1.34rem;
      display: flex;
    }
    .van-empty__description {
      margin-top: 0.24rem;
      font-size: .14rem;
      color: #999999;
      white-space: pre-wrap;
      text-align: center;
    }
  }
}
</style>