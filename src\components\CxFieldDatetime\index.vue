<template>
  <div>
    <van-field v-bind="vanFieldProps" v-model="dateString" :label="label" :placeholder="placeholder"
      :disabled="this.$attrs.disabled" :clickable="!this.$attrs.disabled" :is-link="!this.$attrs.disabled"
      :rules="rules" @click="showPopup" :required="required" readonly />
    <p v-show="tips" class="tips">* 此就诊时间为期望就诊时间，具体以线下预约时间为准</p>
    <!-- 选择生日底部弹窗 -->
    <van-popup v-model="isShowPopup" position="bottom">
      <van-datetime-picker v-bind="dateProps" v-if="type == 'date'" v-model="currentDate" @confirm="handleConfirm"
        @cancel="handleCancel" :min-date="minDate" :max-date="maxDate" :filter="filter" @change="getChange" />
      <van-datetime-picker v-if="type == 'time'" :type="type" v-model="currentTime" @confirm="handleConfirm"
        @cancel="handleCancel" :min-hour="minTime.split(':')[0]" :max-hour="maxTime.split(':')[0]" />
      <van-datetime-picker v-if="type == 'datetime'" :type="type" v-model="currentDate" @confirm="handleConfirm"
        @cancel="handleCancel" :min-date="minDate" :max-date="maxDate" :filter="filter" @change="getChange" />
    </van-popup>
  </div>
</template>

<script>
import { getDaysInMonth } from '../../utils/tools'
export default {
  name: 'CxFieldDatetime',
  model: {
    prop: 'value',
    event: 'change'
  },
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    value: {
      type: [Date, String],
      default: () => {
        // return new Date()
        return ''
      }
    },
    // 返回值是否需要格式化
    isFormatDate: {
      type: Boolean,
      default: true
    },
    // van-field 组件所有属性对象
    vanFieldProps: {
      type: [Object],
      default() {
        return {}
      }
    },
    // van-field 组件常用属性
    label: {
      type: [String],
      default: ''
    },
    placeholder: {
      type: [String],
      default: ''
    },
    // 字段验证规则
    rules: {
      type: [Array],
      default() {
        return []
      }
    },
    required: {
      type: Boolean,
      default: false
    },
    tips: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'date'
    },
    minTime: {
      type: String,
      default: '00:00'
    },
    maxTime: {
      type: String,
      default: '24:00'
    },
    minDate: {
      type: Date,
      default: new Date()
    },
    maxDate: {
      type: Date,
      default: () => new Date(2100, 0, 1)
    },
    haveDate: {
      type: Boolean,
      default: false
    },
    rightCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isShowPopup: false,
      currentTime: '',
      currentDate: new Date(), // van-datetime-picker 组件绑定的值
      dateString: '', // 格式化后的日期
      changeDate: '',
      changeHour: '',
      changeYear: '',
      changeMonth: '',
    }
  },
  computed: {
    // 合并后的 van-datetime-picker 组件属性（默认为date类型）
    dateProps() {
      return {
        type: this.type,
        ...this.$attrs
      }
    },
  },
  watch: {
    value() {
      // 根据value计算相应的值
      if (this.type == 'date' || this.type == 'datetime') {
        this.computeOfValue()
      }
    },
  },
  created() {
    // 根据value计算相应的值
    if (this.type == 'date' || this.type == 'datetime') {
      this.computeOfValue()
    }
  },
  methods: {
    // 显示日期弹窗
    showPopup() {
      if (!this.$attrs.disabled) {
        this.isShowPopup = true
      }
    },
    // 确认选中的生日
    handleConfirm(date, value) {
      console.log("date", date, value)
      if (this.type == 'time') {
        this.dateString = date
        this.$emit('change', this.dateString)
      } else if (this.type == 'datetime') {
        // 格式化后的日期
        this.dateString = this.formatDate(this.currentDate)
        console.log("this.dateString", this.dateString)
        // 需要格式化，则返回格式化后的字符串数据，否则返回date类型原始数据
        if (this.isFormatDate) {
          this.$emit('change', this.dateString)
        } else {
          this.$emit('change', date)
        }
      } else {
        // 格式化后的日期
        this.dateString = this.formatDate(this.currentDate)
        // 需要格式化，则返回格式化后的字符串数据，否则返回date类型原始数据
        if (this.isFormatDate) {
          this.$emit('change', this.dateString)
        } else {
          this.$emit('change', date)
        }
      }
      this.isShowPopup = false
    },
    // 取消
    handleCancel() {
      this.isShowPopup = false
      this.dateString = ''
      this.$emit('change', '')
    },
    // 根据value计算相应的值
    computeOfValue() {
      if (this.value) {
        if (typeof this.value === 'string') {
          // 把‘-’替换成'/'是为了兼容ISO手机
          let date = new Date(this.value.replace(/-/g, '/'))
          // 如果未转换成有效日期数据，则判断是否是时间格式，否则报错
          if (date == 'Invalid Date') {
            if (this.value.indexOf(':') > -1) {
              date = this.value
            } else {
              console.error('cx-field-dateTime组件只接受日期格式数据')
            }
          }
          this.currentDate = date
        } else {
          this.currentDate = this.value
        }
        this.dateString = this.formatDate(this.currentDate)
      }
    },
    getChange(picker) {
      let nowCelect = picker.getValues()
      this.changeYear = nowCelect[0]
      this.changeMonth = nowCelect[1]
      this.changeDate = nowCelect[2]
      this.changeHour = nowCelect[3]
    },
    filter(type, options) {
      if (!this.haveDate) return options
      var changeYear = this.changeYear ? this.changeYear : new Date(this.minDate).getFullYear()
      var changeMonth = this.changeMonth ? this.changeMonth : new Date(this.minDate).getMonth() + 1
      var changeDate = this.changeDate ? this.changeDate : new Date(this.minDate).getDate()
      var changeHour = this.changeHour ? this.changeHour : new Date(this.minDate).getHours()
      if (this.rightCode === 'PR1922') {
        if (new Date(this.minDate).getHours() > 16 || new Date(this.minDate).getHours() < 13) {
          changeHour = this.changeHour ? this.changeHour : 13
          return this.handlePR1922Rules(type, options, changeYear, changeMonth, changeDate, changeHour);
        } else {
          return this.handlePR1922Rules(type, options, changeYear, changeMonth, changeDate, changeHour);
        }
      } else if (this.rightCode === 'PR1878') {
        if (new Date(this.minDate).getHours() > 20 || new Date(this.minDate).getHours() < 8) {
          changeHour = this.changeHour ? this.changeHour : 8
          return this.handleDefaultRules(type, options, changeMonth, changeHour);
        } else {
          return this.handleDefaultRules(type, options, changeMonth, changeHour);
        }
      } else {
        return options
      }
    },
    // 盐城盐家保时间限制
    handlePR1922Rules(type, options, changeYear, changeMonth, changeDate, changeHour) {
      if (changeMonth && type === 'day') {
        var days = getDaysInMonth(changeYear, changeMonth, 5)
        console.log("changeDate", options, days)
        if (new Date(this.minDate).getHours() > 16 || new Date(this.minDate).getHours() < 13) {
          return options.slice(1).filter(option => days.includes(option))
        } else {
          return options.filter(option => days.includes(option))
        }
      }
      if (type == 'hour') return options.filter(option => Number(option) > 12 && Number(option) < 17)
      if (type == 'minute') {
        if (changeHour == 13) return options.filter(option => option > 29);
        if (changeHour == 16) return options.filter(option => option == 0);
      }
      return options
    },
    // 太爱宠时间限制
    handleDefaultRules(type, options, changeMonth, changeHour) {
      console.log("changeHour", changeHour, this.minDate)
      if (type == 'day' && changeMonth) {
        if (new Date(this.minDate).getHours() > 20 || new Date(this.minDate).getHours() < 8) {
          return options.slice(1)
        }
      }
      if (type == 'hour') return options.filter(option => Number(option) > 7 && Number(option) < 21)
      if (type == 'minute') {
        if (changeHour == 8) return options.filter(option => option > 29)
        if (changeHour == 20) return options.filter(option => option == 0)
      }
      return options
    },
    // 格式化日期数据
    formatDate(dateData) {
      let year = dateData.getFullYear().toString()        // 年
      let month = (dateData.getMonth() + 1).toString()    // 月
      let date = dateData.getDate().toString()            // 日
      let H = dateData.getHours().toString()              // 时
      let M = dateData.getMinutes().toString()            // 分
      let S = dateData.getSeconds().toString()            // 秒

      // 如果是个数，则前补0
      month = this.beforeFill0(month)
      date = this.beforeFill0(date)
      H = this.beforeFill0(H)
      M = this.beforeFill0(M)
      S = this.beforeFill0(S)

      let stringDate = ''
      if (this.dateProps.type === 'datetime') {
        stringDate = `${year}-${month}-${date} ${H}:${M}:${S}`;
        // stringDate = `${year}-${month}-${date} ${H}:${M}`;
      } else if (this.dateProps.type === 'datehour') {
        stringDate = `${year}-${month}-${date} ${H}`;
      } else if (this.dateProps.type === 'date') {
        stringDate = `${year}-${month}-${date}`;
      } else if (this.dateProps.type === 'year-month') {
        stringDate = `${year}-${month}`;
      } else if (this.dateProps.type === 'month-day') {
        stringDate = `${month}-${date}`;
      }
      console.log(stringDate)
      return stringDate;
    },
    // 前补0方法
    beforeFill0(num) {
      if (num < 10) {
        return '0' + num
      } else {
        return num
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tips {
  font-size: .12rem;
  color: #00AD78;
  line-height: .16rem;
  padding-bottom: .1rem
}
</style>
