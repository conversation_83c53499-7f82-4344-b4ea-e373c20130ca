<template>
  <div class="reserveSuccess">
    <img src="~images/common/activeSuccess.png" class="top_img">
    <p class="content">您的预约信息已提交， <br>我们会尽快联系您，请您耐心等待！</p>
    <van-button type="primary" class="reserve_btn order" @click="to('/health-atb2023-orders')">查看预约信息</van-button>
  </div>
</template>
<script>
export default {
  name: '',
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    to(path) {
      this.$router.push(path)
    }
  },
}
</script>
<style lang="less" scoped>
.reserveSuccess {
  background: linear-gradient(360deg, #FFFFFF 0%, #FFF5F5 100%);
  height: 100vh;
  padding-top: .4rem;
  .top_img {
    width: 2.47rem;
    display: block;
    margin: 0 auto .2rem
  }
  .content {
    color: #666666;
    text-align: center;
    line-height: .22rem;
  }
  .reserve_btn {
    width: 2.95rem;
    height: .48rem;
    border-radius: .24rem;
    margin: 0.16rem .4rem 0;
    font-size: .18rem;
    font-weight: 500
  }
  .order {
    margin-top: .4rem;
    background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
    border: none;
  }
}
</style>