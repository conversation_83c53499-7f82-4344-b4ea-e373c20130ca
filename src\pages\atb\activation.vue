<template>
  <div class="active">
    <div style="flex: 1;overflow-y: auto;">
      <div class="banner"></div>

      <div class="active_box">
        <div class="title">
          <div class="tit-con">
            <i></i>
            <span>绑定权益人</span>
          </div>
        </div>
        <p class="content">*请输入爱她保被保人信息，其他用户暂不可使用该服务。</p>
        <van-form ref="activeInfo">
          <van-field
            v-model="params.bindName"
            label="姓名"
            placeholder="请输入姓名"
            clearable
            :rules="validateRules.nameRules"
          />
          <cx-field-select
            label="证件类型"
            placeholder="请选择证件类型"
            optionsTitle="请选择证件类型"
            v-model="params.bindIdType"
            :options="idTypeList"
            :rules="validateRules.idTypeRules"
            style="border-bottom: 1px solid rgba(235,237,240, .5)"
          />
          <van-field
            v-model="params.bindIdNum"
            label="证件号码"
            placeholder="请输入证件号码"
            clearable
            :rules="idNumRules"
          />
        </van-form>
      </div>
    </div>

    <div class="footer">
      <div class="module">
        <read-agreement :confirmRead.sync="confirmRead" :agreementQuery="agreeUrlQuery" :color="textColor"/>
      </div>
      <van-button :disabled="!completed" class="btn" @click="active">激活权益</van-button>
    </div>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import { idTypeList } from './js/idType'
import readAgreement from '@/pages/components/readAgreement'
import validateRules from '@/utils/fieldValidateRules'
import { getValueByKeyFromURL } from '@/utils/tools'
export default {
  components: { readAgreement },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"103",keys:"sysm"},
      textColor:"#F34168",
      toPath: "",
      params: {
        cardTypeCode: "103",
        bindName: "",
        bindIdType: "1",
        bindIdNum: "",
      },
      idTypeList: idTypeList, // 证件类型
      confirmRead: false, // 阅读协议
      validateRules, // 校验规则
    }
  },
  computed: {
    // 证件号校验规则
    idNumRules() {
      const idType = this.params.bindIdType.toString()
      if (idType === '1') {
        // 身份证校验规则(常规通用校验+未成年校验)
        return validateRules.idNumRules
      } else if (idType === '2') {
        // 护照校验规则
        return validateRules.passportRules
      } else {
        // 港澳居民来往内地通行证
        return validateRules.otherIdNumRules
      }
    },
    completed() {
      return this.params.bindName && this.params.bindIdNum && this.confirmRead
    }
  },
  watch: {},
  created() {
    if(getValueByKeyFromURL(location.href, 'toPath')) {
      this.toPath = getValueByKeyFromURL(location.href, 'toPath')
    } else {
      this.toPath = "origin"
    }
  },
  mounted() {},
  methods: {
    active() {
      this.$refs.activeInfo.validate().then(() => {
        var arr = []
        arr[0] = this.params
        api.activateATB(arr).then(() => {
          this.getPvUv(this.toPath == 'trt' ? 'PR011' : 'PR012')
        })
      })
    },
    getPvUv(rightCode) {
      api.getPvUv({ eventOwner: '103', eventCode: rightCode, eventType: 'RIGHTS_ACTIVE'}).then(() => {
        this.queryFirstCard()
      })
    },
    queryFirstCard() {
      api.queryCardList({cartTypeCode: "103"}).then((res) => {
        if(this.toPath == "trt" || this.toPath == 'asqy') {
          this.$router.push({
            path: this.toPath == "trt" ? "/health-atb-introTRT" : "/health-atb-introduction",
            query: {
              cardNo: res.result[0].cardNo
            }
          })
        } else {
          this.$router.push("/health-atb-origin")
        }
      })
    }
  },
}
</script>
<style lang="less" scoped>
.active {
  height: 100vh;
  background: #FCFCFC;
  display: flex;
  flex-direction: column;
  .banner {
    width: 3.75rem;
    height: 2.64rem;
    margin: 0 auto;
    background: url('../../assets/images/atb/activate-banner1.png') no-repeat;
    background-size: 100%;
  }
  .active_box {
    width: 3.43rem;
    padding: .16rem 0 0;
    border-radius: 0.12rem;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    margin: -0.3rem auto .16rem;
    .title {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.22rem;
      display: flex;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: #F34168;
          border-radius: 0.02rem;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    .content {
      color: #F34168;
      font-size: .12rem;
      padding: 0.12rem .16rem 0;
      line-height: .17rem;
    }
    /deep/.van-form {
      padding: 0 .16rem;
      .van-field {
        padding: .16rem 0
      }
      .van-field .van-field__label {
        width: .88rem;
        margin-right: 0;
        color: #666666
      }
      .van-cell {
        align-items: center;
      }
      .van-cell::after {
        left: 0;
        right: 0
      }
      .van-field__button {
        padding: 0.06rem 0.1rem;
        background: #F34168;
        border-radius: .18rem;
      }
      .van-field .van-cell__right-icon {
        bottom: 16px;
        right: 0;
      }
    }
  }
  .footer {
    width: 100%;
    height: 1.4rem;
    background: #fff;
    .module {
      display: flex;
      background: #fff;
      padding: 0.12rem 0.16rem;
      box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
    }
    .btn {
      width: 3.43rem;
      height: .48rem;
      background: #F34168;
      border-radius: .24rem;
      border: none;
      margin: .16rem .16rem;
      color: #fff;
      font-size: .18rem;
      line-height: .24rem;
      font-weight: 500
    }
    /deep/.van-button--disabled {
      background: #CCCCCC !important;
      border: none !important
    }
  }
}
</style>