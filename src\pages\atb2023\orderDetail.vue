<template>
  <div class="orderDetail">
    <div class="status_block">
      <img src="~images/atb2023/orderDetail-status.png" class="status_icon">
      <p class="status_text" :style="{color: authColor}">{{ dictObj[orderInfo.status] }}</p>
      <svg-icon iconName="callCenter" :style="{color: authColor}" />
      <van-button type="text" class="callCenter" @click="callShow = true" :style="{color: authColor}">联系客服</van-button>
    </div>
    <div class="card_info" v-if="orderInfo">
      <div class="title">
        <div class="tit-con">
          <i :style="{ background: authColor }"></i>
          <span>预约状态</span>
        </div>
      </div>

      <div class="process">
        <atb-process :completed="completed" :options="options" :marginStyle="'.1rem 0.2rem 0'"></atb-process>
      </div>
      <p class="latestTime">{{latestTime}}</p>
      <div class="latestInfo">
        <p class="name" :style="{color: authColor}">{{dictObj[orderInfo.status]}}</p>
        <p class="remark">{{latestRemark}}</p>
      </div>
      <div v-if="orderInfo.confirmInfos && orderInfo.confirmInfos.length > 0">
        <div v-for="(item, index) in orderInfo.confirmInfos" :key="index" style="margin: 0 .16rem;border-top: 1px solid #F4F4F4">
          <p class="confirmInfo_text" style="padding-top: .16rem;color: #1669E3" v-show="item.confirmItemName">{{item.confirmItemName}}</p>
          <p class="confirmInfo_text" :style="{'padding-top': orderInfo.pbmCardUser.cardTypeCode === '124' || orderInfo.pbmCardUser.cardTypeCode === '125' ? '0' : '.16rem'}">{{orderInfo.pbmCardUser.cardTypeCode === '124' || orderInfo.pbmCardUser.cardTypeCode === '125' ? '预约机构' : '预约医院'}}：{{item.confirmHospital}}</p>
          <p class="confirmInfo_text" v-show="item.confirmDept">预约科室：{{item.confirmDept}}</p>
          <p class="confirmInfo_text" v-show="item.confirmDoctor">预约医生：{{item.confirmDoctor}}</p>
          <p class="confirmInfo_text" v-show="item.confirmDate">{{orderInfo.pbmCardUser.cardTypeCode === '124' || orderInfo.pbmCardUser.cardTypeCode === '125' ? '预约时间' : '就诊时间'}}：{{item.confirmDate}}</p>
          <p class="confirmInfo_text" v-show="item.remark">预约信息：{{item.remark}}</p>
          <p class="confirmInfo_subText" v-show="index == orderInfo.confirmInfos.length - 1">*工作人员将在就诊前一天电话提醒您。</p>
        </div>
      </div>
    </div>

    <div class="card_info">
      <div class="title">
        <div class="tit-con">
          <i :style="{ background: authColor }"></i>
          <span>预约信息</span>
        </div>
      </div>
      <p class="rightName">{{orderInfo.rightName}}</p>
      <p class="text">就诊人：{{orderInfo.bindName}}</p>
      <p class="text">手机号：{{orderInfo.phoneNo}}</p>
      <p v-if="orderInfo.rescueDistrict" class="text">医疗救护地区：{{orderInfo.rescueDistrict}}</p>
      <p v-if="orderInfo.rescueAddress" class="text">详细地址：{{orderInfo.rescueAddress}}</p>
      <p v-if="orderInfo.rescueHospital" class="text">就诊医院：{{orderInfo.rescueHospital}}</p>
      <p v-if="orderInfo.nurseDistrict" class="text">照护地区：{{orderInfo.nurseDistrict}}</p>
      <p v-if="orderInfo.nurseAddress" class="text">详细地址：{{orderInfo.nurseAddress}}</p>
      <p v-if="orderInfo.hospitalName" class="text">就诊医院：{{orderInfo.hospitalName}}</p>
      <p v-if="orderInfo.hospitalDept" class="text">就诊科室：{{orderInfo.hospitalDept}}</p>
      <p v-if="orderInfo.mediTime" class="text">就诊时间：{{orderInfo.mediTime.split(' ')[0]}}</p>
      <p v-if="orderInfo.payCode" class="text">疫苗型号：{{orderInfo.payCode}}</p>
      <p v-if="orderInfo.province || orderInfo.serviceCity" class="text">服务区域：{{orderInfo.province + orderInfo.serviceCity}}</p>
      <img class="rightCode" v-if="orderInfo.rightCode" :src="orderInfo.rightIcon" alt="">
    </div>

    <div class="card_info" v-if="orderInfo.files && orderInfo.files.length > 0" style="margin-bottom: .64rem">
      <div class="title">
        <div class="tit-con">
          <i :style="{ background: authColor }"></i>
          <span>材料信息</span>
        </div>
      </div>
      <div class="files" v-for="(item, index) in fieldNameList" :key="index">
        <p class="fieldName">{{dictImg[item]}}</p>
        <div v-if="reasonList">
          <p class="reason" v-for="(s, t) in reasonList[item]" :key="t">补充原因：{{s.reason}}</p>
        </div>
        <img @click="bigShow(m.publicFileURL)" class="file_img" :src="m.publicFileURL" v-for="(m, n) in files[item]" :key="n">
      </div>
    </div>

    <div class="footer">
      <van-button
        type="primary"
        class="btn"
        v-show="orderInfo.canEdit"
        @click.stop="cancel()" :style="{background: authBgColor}"
      >取消预约</van-button>
      <van-button type="primary" class="btn" v-show="orderInfo.status == 'WAITPAY'" @click="pay" :style="{background: authBgColor}">去支付</van-button>
      <van-button type="primary" class="btn" v-show="orderInfo.canEdit&&orderInfo.status != 'WAITPAY'" @click="toSupplement" :style="{background: authBgColor}">修改或补充申请材料</van-button>
    </div>

    <!-- 样张弹窗 -->
    <van-popup v-model="caseShow">
      <img :src="casePath" style="width: 2.75rem">
    </van-popup>
    <!-- 客服弹窗 -->
    <van-popup v-model="callShow" class="dialog" :close-on-click-overlay="false">
      <p class="content call_title">温馨提示</p>
      <p class="content phone">客服热线：************</p>
      <van-button class="call_cancel" @click="callShow = false">取消</van-button>
      <van-button class="call_confirm" @click="call('**********')">立即拨打</van-button>
    </van-popup>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import api_global from '@/api/global'
import { theme } from '@/mixin'
import atbProcess from './components/process.vue'
export default {
  mixins: [theme],
  components: { atbProcess },
  data() {
    return {
      id: this.$route.query.id || "",
      orderInfo: {},
      dictObj: {}, // 状态字典
      dictImg: {}, // 材料字典
      completed: Number(this.$route.query.completed), // process步骤
      files: {},
      reasonList: {},
      fieldNameList: [],
      caseShow: false,
      casePath: "",
      options: [{
        name: "申请中"
      }, {
        name: '服务中'
      }, {
        name: '已完成'
      }],
      latestTime: "",
      latestRemark: "",
      callShow: false, // 客服弹窗
    }
  },
  computed: {
    authColor() {
      return '#EB2A5C'
    },
    authBgColor() {
      return 'linear-gradient(270deg, #FD7570 0%, #ED5075 100%)'
    },
  },
  mounted() {
    this.queryDictObject()
  },
  methods: {
    queryDictObject() {
      api_global.queryDictObject('PBM_CARD_BOOKING_IMAGE_TYPES').then((resp) => {
        this.dictImg = resp.result
      })
      api_global.queryDictObject('PBM_CARD_BOOKING_STATUS').then((res) => {
        this.dictObj = res.result;
        this.queryOrderDetail()
      })
    },
    queryOrderDetail() {
      api.queryOrderDetail({id:this.id}).then(res => {
        console.log(this.fieldNameList)
        this.orderInfo = res.result
        res.result.files.forEach(ele => {
          let path = this.files[ele.fieldName]
          if(!path){
            path = [];
          }
          path.push(ele);
          this.files[ele.fieldName]=path;

          this.fieldNameList.push(ele.fieldName)
        });
        let arr = new Set(this.fieldNameList)
        this.fieldNameList = Array.from(arr)

        if(res.result.addInfos) {
          res.result.addInfos.forEach(ele => {
            let reason = this.reasonList[ele.fieldName]
            if(!reason) {
              reason = []
            }
            reason.push(ele)
            this.reasonList[ele.fieldName] = reason
          })
        }
        this.latestTime = this.orderInfo.auditLogs.length > 0 ? this.orderInfo.auditLogs[this.orderInfo.auditLogs.length - 1].updateTime : this.orderInfo.createTime
        switch(this.orderInfo.status) {
          case 'UNPROCESSED':
            this.latestRemark = '审核中';
            break;
          case 'WAITPAY':
          this.latestRemark = '';
            break;
          case 'CONFIRM':
          case 'TOADD':
            this.latestRemark = this.orderInfo.addInfos && this.orderInfo.addInfos[this.orderInfo.addInfos.length - 1].reason
            break;
          default:
            this.latestRemark = this.orderInfo.auditLogs && this.orderInfo.auditLogs[this.orderInfo.auditLogs.length - 1].remark
        }
      })
    },
    bigShow(path) {
      this.caseShow = true;
      this.casePath = path
    },
    toSupplement() {
      this.$router.push({
        path: '/health-atb2023-supplement',
        query: {
          cardNo: this.orderInfo.cardNo,
          id: this.orderInfo.id
        }
      })
    },
    cancel() {
      api.cancel({ id: this.orderInfo.id }).then(() => {
        this.$toast('预约取消成功')
        this.files = []
        this.fieldNameList = []
        this.reasonList = []
        this.queryOrderDetail()
      })
    },
    call(tel) {
      window.location.href = 'tel:' + tel
    },
    pay() {
      api.hpvPay(this.id).then((res) => {
        location.href = res.result.url
      })
    }
  },
}
</script>
<style lang="less" scoped>
.orderDetail {
  padding: .16rem;
  background: url('../../assets/images/common/com-bg.png') right top/1.36rem 1.4rem no-repeat , linear-gradient(360deg, #FFFFFF 0%, #FFF5F5 100%);
  overflow: auto;
  .status_block {
    display: flex;
    align-items: center;
    margin-bottom: .16rem;
    .status_icon {
      width: .28rem;
      height: .28rem;
    }
    .status_text {
      font-size: .22rem;
      line-height: .3rem;
      font-weight: bold;
      margin-left: .08rem;
      flex: 1
    }
    .svg-icon {
      width: 0.24rem;
      height: 0.24rem
    }
    .callCenter {
      width: auto;
      height: auto;
      border: none;
      background: transparent;
      padding: 0;
      margin: 0 0 0 0.04rem;
      font-size: .12rem;
      line-height: .18rem;
    }
  }

  .card_info {
    width: 3.43rem;
    padding: .16rem 0 0;
    border-radius: 0.12rem;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    margin-bottom: .12rem;
    position: relative;
    .title {
      font-size: 0.16rem;
      font-weight: 500;
      line-height: 0.22rem;
      display: flex;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: @primary-color;
          border-radius: 0px 2px 2px 0px;;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    .process {
      background: #FAFAFA;
      border-radius: .08rem;
      margin: .16rem .16rem .2rem;
      padding: .12rem .16rem;
      align-items: center;
      display: flex;
    }
    .latestTime {
      padding: 0 .16rem;
      color: #666666;
      line-height: .2rem;
    }
    .latestInfo {
      display: flex;
      align-items: center;
      line-height: 20px;
      padding: .08rem .16rem .16rem;
      .name {
        color: @primary-color;
        margin-right: .4rem
      }
      .remark {
        color: #666666;
      }
    }
    .rightName {
      font-weight: 500;
      line-height: .2rem;
      padding: .16rem .16rem .12rem;
      font-size: .14rem;
    }
    .text {
      color: #666666;
      line-height: .2rem;
      padding: 0 .16rem .12rem;
    }
    .rightCode {
      width: .6rem;
      position: absolute;
      right: .16rem;
      top: .55rem;
    }
    .files {
      border-bottom: 1px solid #F4F4F4;
      margin: 0 .16rem;
      padding: .16rem 0;
      .fieldName {
        color: #666666;
        line-height: .2rem;
        padding-bottom: .08rem
      }
      .reason {
        color: #D70E0E;
        font-size: .12rem;
        line-height: .18rem;
        padding-bottom: .08rem
      }
      .file_img {
        width: .8rem;
        height: .8rem;
        display: inline-block;
        vertical-align: middle;
        border-radius: .08rem;
        margin-right: .12rem
      }
    }
    .files:last-child {
      border-bottom: none
    }
    .confirmInfo_text {
      line-height: .2rem;
      padding: 0 0 .12rem;
    }
    .confirmInfo_subText {
      color: @primary-color;
      line-height: .18rem;
      padding: 0 0 .2rem;
    }
  }
  .footer {
    width: 100%;
    height: .64rem;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #F4F4F4;
    background: #fff;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .btn {
      height: .32rem;
      padding: .06rem .12rem;
      border-radius: .16rem;
      line-height: .2rem;
      font-weight: 500;
      margin-right: .12rem;
      border: none
    }
  }

  .dialog {
    background: #fff;
    width: 3.11rem;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    border-radius: .24rem;
    padding: .24rem 0;
    text-align: center;
    .content {
      font-size: .2rem;
      line-height: .28rem;
      text-align: center;
      margin-bottom: .32rem;
    }
    .call_title {
      font-weight: 500
    }
    .phone {
      margin-bottom: .52rem
    }
    .call_cancel {
      width: 1.24rem;
      height: .48rem;
      background: #EEEEEE;
      border-radius: .24rem;
      padding: 0;
      margin: 0;
      border: none;
      color: #999999;
      font-size: .18rem;
      line-height: .24rem;
    }
    .call_confirm {
      width: 1.24rem;
      height: .48rem;
      background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);
      border-radius: .24rem;
      padding: 0;
      margin: 0 0 0 .15rem;
      border: none;
      color: #fff;
      font-size: .18rem;
      line-height: .24rem;
    }
  }
}
</style>