@import "./variate.less";

// 全局样式
.clearfix {
  zoom: 1;
}
.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}

// 验证错误提示语位置调整

.van-field {
  // flex-direction: column;
  padding: 0.13rem 0px;
  // border-bottom: 0.01rem solid #f4f4f4;

  .van-field__label {
    font-size: 0.14rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .van-field__control {
    // font-size: 0.16rem;
    &:disabled {
      color: #888;
      -webkit-text-fill-color: #888;
    }
    // 强制固定placeholder颜色，错误时不显示红色
    &::placeholder {
      color: #ccc;
    }
  }
  .van-field__error-message {
    line-height: 12px;
    position: absolute;
    bottom: -0.1rem;
    left: 0px;
  }
  .van-cell__right-icon {
    position: absolute;
    bottom: 10px;
    right: 16px;
  }
}

.van-button--primary {
  color: #fff;
  background: @btn-bgColor;
}
.van-button--plain.van-button--primary {
  color: #1669E3;
  background: #fff
}

// 背景变换
.com-bg {
  // background: url('../../assets/images/common/com-bg.png') right top/1.36rem 1.4rem no-repeat , linear-gradient(360deg, #FFFFFF 0%, #E9F4FF 100%);
  background: linear-gradient(360deg, #FFFFFF 0%, #E9F4FF 100%);
  height: 100vh;
  overflow: auto;
}
.com-block {
  // width: 375px;
  background: rgba(255,255,255,0.5);
  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
  border-radius: .24rem .24rem 0px 0px;
  padding: .12rem 0;
  margin-top: .16rem
}

.but_round {
  position: relative;
  z-index: 10
}
.but_round::after {
  content: "";
  position: absolute;
  z-index: -1;
  left:0;
  bottom: 0;
  width: 100%;
  height: .08rem;
  background: linear-gradient( 270deg, #FFFFFF 0%, #F98E22 100%)
}