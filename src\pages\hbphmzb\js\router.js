const router = [{
  name: 'health-hbphmzb-activation',
  path: '/health-hbphmzb-activation',
  component: () => import( /* webpackChunkName: "health-hbphmzb-activation" */ '@/pages/hbphmzb/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-hbphmzb-origin',
  path: '/health-hbphmzb-origin',
  component: () => import( /* webpackChunkName: "health-hbphmzb-origin" */ '@/pages/hbphmzb/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-hbphmzb-introduction',
  path: '/health-hbphmzb-introduction',
  component: () => import( /* webpackChunkName: "health-hbphmzb-introduction" */ '@/pages/hbphmzb/introduction'),
  meta: {
    title: '权益介绍',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
},];
export default router;