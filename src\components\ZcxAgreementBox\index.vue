<template>
  <div class="agreement-box" :class="isInline ? 'inline' : 'flex'">
    <ul :style="{ color: color }">
      <li v-for="(item, index) of agreements" :key="index" @click="show(item)">《{{ item.name }}》</li>
    </ul>

    <!-- 协议弹窗 -->
    <zcx-agreement-popup v-model="showPopup" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <keep-alive>
          <component v-bind:is="currentComponent" class="agreementBox"></component>
        </keep-alive>
      </div>
    </zcx-agreement-popup>

    <!-- pdf、协议弹窗 -->
    <zcx-agreement-popup v-model="pdfShow" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <div class="agreement">
          <iframe v-if="!isPDF" :src="currentComponent" class="htmlBox" frameborder="0"></iframe>
          <pdf v-else class="pdfBox" v-for="item in numPages" :key="item" :src="currentComponent" :page="item"></pdf>
        </div>
      </div>
    </zcx-agreement-popup>
  </div>
</template>

<script>
import { theme, product } from '@/mixin'
import pdf from 'vue-pdf'
import ZcxAgreementPopup from '../ZcxAgreementPopup'
import api from '@/api/cityHealth'
// 协议文档
// import ysxy from '@/agreement/agree_ysxy'
// import ptfwxy from '@/agreement/agree_ptfwxy'
// 惠邕保
// import hyb_zcxy from '@/agreement/hyb_zcxy.vue'
// import hyb_yszc from '@/agreement/hyb_yszc.vue'
// 爱她保
// import atb_zcxy from '@/agreement/atb_zcxy'
// import atb_yszc from '@/agreement/atb_yszc'
// 京惠保
// import jhb_hdgz from '@/agreement/jhb_hdgz'
// import jhb_zcxy from '@/agreement/jhb_zcxy'
// import jhb_yszc from '@/agreement/jhb_yszc'
// 达惠保
// import dhb_zcxy from '@/agreement/dhb_zcxy.vue'
// import dhb_yszc from '@/agreement/dhb_yszc.vue'
// 惠州惠医保
// import hzhyb_sysm from '@/agreement/hzhyb_sysm'
// import hzhyb_zcxy from '@/agreement/hzhyb_zcxy'
// import hzhyb_ysxy from '@/agreement/hzhyb_ysxy'
// 惠绵保
// import hmb_zcxy from '@/agreement/hmb_zcxy'
// import hmb_ysxy from '@/agreement/hmb_ysxy'
// 宜昌2024
// import yc2024_ysxy from '@/agreement/yc2024_ysxy'
// import yc2024_zcxy from '@/agreement/yc2024_zcxy'

export default {
  mixins: [theme, product],
  name: 'ZcxAgreementBox',
  components: {
    ZcxAgreementPopup,
    // ysxy,
    // ptfwxy,
    // hyb_zcxy,
    // hyb_yszc,
    // atb_zcxy,
    // atb_yszc,
    // jhb_hdgz,
    // jhb_zcxy,
    // jhb_yszc,
    // dhb_zcxy,
    // dhb_yszc,
    // hzhyb_sysm,
    // hzhyb_zcxy,
    // hzhyb_ysxy,
    // hmb_zcxy,
    // hmb_ysxy,
    // yc2024_ysxy,
    // yc2024_zcxy,
    pdf
  },
  props: {
    // 是否是行内样式布局
    isInline: {
      type: Boolean,
      default: false
    },
    productName: {
      type: String,
      default: null
    },
    color: {
      type: String,
      default: ""
    },
    queryParams: {
      type: Object,
    }
  },
  data() {
    return {
      showPopup: false, // 协议弹窗的显示隐藏
      pdfShow: false, // pdf协议弹窗
      currentComponent: '', // 当前组件
      currentName: '', // 当前组件名字
      agreements: [],
      // 协议（静态文件）列表
      agreementList: [
        {
          name: "宸汐健康管家隐私政策",
          key: "ysxy",
          productName: "null",
          cardTypeCode: "0000",
        }, {
          name: "宸汐健康管家注册协议",
          key: "ptfwxy",
          productName: "null",
          cardTypeCode: "0000",
        }, {
          name: "湖北普惠门诊保健康管理服务会员注册协议",
          key: "hbphmzb_zcxy",
          productName: "hbphmzb",
          cardTypeCode: '209',
        },
        {
          name: "惠邕保健康管理服务会员注册协议",
          key: "hyb_zcxy",
          productName: "hyb",
          cardTypeCode: "104",
        }, {
          name: "隐私政策",
          key: "hyb_yszc",
          productName: "hyb",
          cardTypeCode: "104",
        }
        , {
          name: "爱她保健康管理服务会员注册协议",
          key: "atb_zcxy",
          productName: "atb",
          cardTypeCode: "103",
        }, {
          name: "隐私政策",
          key: "atb_yszc",
          productName: "atb",
          cardTypeCode: "103",
        }, {
          name: "同仁堂中医药专属福利活动规则",
          key: "jhb_hdgz",
          productName: "jhb",
          cardTypeCode: "105",
        }, {
          name: "注册协议",
          key: "jhb_zcxy",
          productName: "jhb",
          cardTypeCode: "105",
        }, {
          name: "隐私政策",
          key: "jhb_yszc",
          productName: "jhb",
          cardTypeCode: "105",
        }, {
          name: "达惠保健康管理服务会员注册协议",
          key: "dhb_zcxy",
          productName: "dhb",
          cardTypeCode: "106",
        }, {
          name: "隐私政策",
          key: "dhb_yszc",
          productName: "dhb",
          cardTypeCode: "106",
        }, {
          name: "注册协议",
          key: "hzhyb_zcxy",
          productName: "hzhyb",
          cardTypeCode: "123",
        }, {
          name: "隐私政策",
          key: "hzhyb_ysxy",
          productName: "hzhyb",
          cardTypeCode: "123",
        }, {
          name: "注册协议",
          key: "ptfwxy",
          productName: "hqb"
        }, {
          name: "隐私政策",
          key: "ysxy",
          productName: "hqb",
          cardTypeCode: "0000",
        }, {
          name: "隐私政策",
          key: "hmb_ysxy",
          productName: "hmb",
          cardTypeCode: "135",
        }, {
          name: "注册协议",
          key: "hmb_zcxy",
          productName: "hmb",
          cardTypeCode: "135",
        }, {
          name: "隐私政策",
          key: "hmb_ysxy",
          productName: "rws",
          cardTypeCode: "136",
        }, {
          name: "注册协议",
          key: "hmb_zcxy",
          productName: "rws",
          cardTypeCode: "136",
        }, {
          name: "隐私政策",
          key: "hmb_ysxy",
          productName: "gaehb",
          cardTypeCode: "141",
        }, {
          name: "注册协议",
          key: "hmb_zcxy",
          productName: "gaehb",
          cardTypeCode: "141",
        }, {
          name: "隐私政策",
          key: "hmb_ysxy",
          productName: "cqykb",
          cardTypeCode: "143",
        }, {
          name: "注册协议",
          key: "hmb_zcxy",
          productName: "cqykb",
          cardTypeCode: "143",
        }, {
          name: "隐私政策",
          key: "hmb_ysxy",
          productName: "yahyb",
          cardTypeCode: "139",
        }, {
          name: "注册协议",
          key: "hmb_zcxy",
          productName: "yahyb",
          cardTypeCode: "139",
        }, {
          name: "隐私政策",
          key: "hmb_ysxy",
          productName: "xhb",
          cardTypeCode: "140",
        }, {
          name: "注册协议",
          key: "hmb_zcxy",
          productName: "xhb",
          cardTypeCode: "140",
        }, {
          name: "隐私政策",
          key: "ysxy",
          productName: "oneStation",
          cardTypeCode: "0000",
        }, {
          name: "注册协议",
          key: "ptfwxy",
          productName: "oneStation"
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "yc2024",
          cardTypeCode: "255",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "yc2024",
          cardTypeCode: "255",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "bs2024",
          cardTypeCode: "268",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "bs2024",
          cardTypeCode: "268",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "cz2024",
          cardTypeCode: "269",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "cz2024",
          cardTypeCode: "269",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "hz2024",
          cardTypeCode: "270",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "hz2024",
          cardTypeCode: "270",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "zy2024",
          cardTypeCode: "306",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "zy2024",
          cardTypeCode: "306",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "yz2024",
          cardTypeCode: "304",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "yz2024",
          cardTypeCode: "304",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "dhb2024",
          cardTypeCode: "365",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "dhb2024",
          cardTypeCode: "365",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "tj2024",
          cardTypeCode: "369",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "tj2024",
          cardTypeCode: "369",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "fcg2024",
          cardTypeCode: "368",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "fcg2024",
          cardTypeCode: "368",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "tk2024",
          cardTypeCode: "371",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "tk2024",
          cardTypeCode: "371",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "my2024",
          cardTypeCode: "381",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "my2024",
          cardTypeCode: "381",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "hzhhb",
          cardTypeCode: "386",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "hzhhb",
          cardTypeCode: "386",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "bhb2024",
          cardTypeCode: "388",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "bhb2024",
          cardTypeCode: "388",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "chb2024",
          cardTypeCode: "398",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "chb2024",
          cardTypeCode: "398",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "yhb2024",
          cardTypeCode: "411",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "yhb2024",
          cardTypeCode: "411",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "hs2024",
          cardTypeCode: "410",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "hs2024",
          cardTypeCode: "410",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "thb2024",
          cardTypeCode: "415",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "thb2024",
          cardTypeCode: "415",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "lb2024",
          cardTypeCode: "416",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "lb2024",
          cardTypeCode: "416",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "gg2024",
          cardTypeCode: "419",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "gg2024",
          cardTypeCode: "419",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "nth2024",
          cardTypeCode: "420",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "nth2024",
          cardTypeCode: "420",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "ykb2025",
          cardTypeCode: "421",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "ykb2025",
          cardTypeCode: "421",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "yc2025",
          cardTypeCode: "422",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "yc2025",
          cardTypeCode: "422",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "hc2025",
          cardTypeCode: "423",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "hc2025",
          cardTypeCode: "423",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "hhb2025",
          cardTypeCode: "424",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "hhb2025",
          cardTypeCode: "424",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "ls2025",
          cardTypeCode: "425",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "ls2025",
          cardTypeCode: "425",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "bh2025",
          cardTypeCode: "427",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "bh2025",
          cardTypeCode: "427",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "jz2025",
          cardTypeCode: "428",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "jz2025",
          cardTypeCode: "428",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "jm2025",
          cardTypeCode: "429",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "jm2025",
          cardTypeCode: "429",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "ya2025",
          cardTypeCode: "431",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "ya2025",
          cardTypeCode: "431",
        }, {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "xg2025",
          cardTypeCode: "430",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "xg2025",
          cardTypeCode: "430",
        },
        {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "ga2024",
          cardTypeCode: "433",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "ga2024",
          cardTypeCode: "433",
        },
        {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "tm2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "tm2025",
          cardTypeCode: "007",
        },
        {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "ah2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "ah2025",
          cardTypeCode: "007",
        },
        {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "zy2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "zy2025",
          cardTypeCode: "007",
        },
        {
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "dhb2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "dhb2025",
          cardTypeCode: "007",
        },{
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "yjb2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "yjb2025",
          cardTypeCode: "007",
        },{
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "xyx2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "xyx2025",
          cardTypeCode: "007",
        },{
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "chb2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "chb2025",
          cardTypeCode: "007",
        },{
          name: "隐私政策",
          key: "yc2024_ysxy",
          productName: "bhb2025",
          cardTypeCode: "007",
        }, {
          name: "注册协议",
          key: "yc2024_zcxy",
          productName: "bhb2025",
          cardTypeCode: "007",
        },
      ],
      isPDF: false,
      numPages: '',
    }
  },
  mounted() {
    var name = ""
    switch (this.productName) {
      case 'hyb':
      case 'atb':
      case 'jhb':
      case 'dhb':
      case 'hzhyb':
      case 'hqb':
      case 'hmb':
      case 'rws':
      case 'gaehb':
      case 'cqykb':
      case 'yahyb':
      case 'xhb':
      case 'oneStation':
      case 'yc2024':
      case 'bs2024':
      case 'cz2024':
      case 'hz2024':
      case 'zy2024':
      case 'yz2024':
      case 'dhb2024':
      case 'tj2024':
      case 'fcg2024':
      case 'tk2024':
      case 'my2024':
      case 'hzhhb':
      case 'bhb2024':
      case 'chb2024':
      case 'yhb2024':
      case 'hs2024':
      case 'thb2024':
      case 'lb2024':
      case 'gg2024':
      case 'nth2024':
      case 'ykb2025':
      case 'yc2025':
      case 'hc2025':
      case 'hhb2025':
      case 'ls2025':
      case 'bh2025':
      case 'jm2025':
      case 'jz2025':
      case 'ya2025':
      case 'xg2025':
      case 'ga2024':
      case 'tm2025':
      case 'ah2025':
      case 'zy2025':
      case 'dhb2025':
      case 'yjb2025':
      case 'xyx2025':
      case 'chb2025':
      case 'xm2025':
      case 'bhb2025':
        name = this.productName
        break;
      default:
        name = 'null'
    }
    let agreementList1 = this.agreementList.filter((item) => item.productName == name)
    console.log("agreementList1>>>>>>>>>>>>", agreementList1)
    console.log("urlquery>>>>>>>>>>>>", this.queryParams)
    var params = "?cardTypeCode=";
    var qp = false;
    if (this.queryParams) {
      qp = true;
    }
    var cardTypeCode = "0000";
    var page = "";
    var keys = "";
    if (agreementList1.length > 0) {
      if (qp) {
        cardTypeCode = this.queryParams.cardTypeCode
        page = this.queryParams.page
        keys = this.queryParams.keys
      } else {
        cardTypeCode = agreementList1[0].cardTypeCode
        page = agreementList1[0].page
      }
      params += cardTypeCode
      if (page && page.length > 0) {
        params += "&page=" + page
      }
      if (keys != null && keys.length > 0) {
        params += "&keys=" + keys
      } else {
        if (this.productName == 'jhb') {
          params += "&keys=zcxy,ysxy,hdgz"
        } else {
          params += "&keys=zcxy,ysxy"
        }
      }
      api.queryAgreementList(params).then((res) => {
        if (res.result) {
          res.result.forEach((ele) => {
            if (ele.typeCode >= 456 && ele.typeCode <= 460) {
              ele.name = ele.name == '服务手册' ? '建档协议' : ele.name
            }
          })
          this.agreements = res.result
        }
      })
    }
  },
  methods: {
    // 显示协议内容
    show(item) {
      this.currentName = item.name
      this.currentComponent = item.url
      const pathname = new URL(item.url).pathname
      this.isPDF = pathname.split('.').pop() == 'pdf'
      console.log("this.isPDF", this.isPDF)
      if (this.isPDF) {
        this.previewPdf(item)
      } else {
        this.pdfShow = true
        // this.showPopup = true
      }
    },
    previewPdf(item) {
      this.currentComponent = pdf.createLoadingTask(item.url)
      this.currentComponent.promise.then(pdf => {
        this.$nextTick(() => {
          this.numPages = pdf.numPages; // pdf总页数
          console.log("this.numPages", this.numPages)
          this.pdfShow = true
        });
      }).catch(() => {
        setTimeout(() => {
          this.$toast("请在手机微信端打开")
        }, 1500);
      })
    },
  }
}
</script>

<style lang="less" scoped>
.agreement-box {
  text-indent: 0;

  &.flex {
    ul {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      font-size: 0.12rem;
      padding: 0 0.15rem;
    }
  }

  &.inline {
    display: inline;

    ul {
      display: inline;

      li {
        display: inline;
      }
    }
  }

  .readPopup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .title {
      height: 0.6rem;
      flex: 1;
      box-sizing: border-box;
      line-height: 0.28rem;
      padding: 0.16rem;
      text-align: center;
      font-weight: 400;
      color: #333333;
      font-size: 0.16rem;
      position: relative;

      h2 {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
    }

    .agreement {
      width: 3.75rem;
      // height: 6.4rem;
      overflow-y: scroll;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;

      .htmlBox {
        width: 100%;
        // height: 100%;
        min-height: 6.4rem;
      }

      .pdfBox {
        width: 110%;
        // height: 100%;
        min-height: 6.4rem;
      }
    }

    .agreementBox {
      flex: 1;
      overflow-y: scroll;
      margin-bottom: 0.1rem;
    }
  }
}
</style>
