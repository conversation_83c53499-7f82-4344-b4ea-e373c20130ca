<template>
  <div class="main">
    <div class="step" v-for="(item, index) in stepList" :key="index">
      <div class="text" v-index="index">
        <p >{{item.name}}</p>
        <img v-show="index < active" src="~images/icon/step_active.png" class="icon">
      </div>
      <div :style="{margin: marginStyle}" v-show="index < stepList.length - 1" class="line"></div>
    </div>
  </div>
</template>
<script>
import { theme } from '@/mixin'
let vm = null
export default {
  mixins: [theme],
  name: '',
  components: {},
  data() {
    return {
      active: this.completed,
      stepList: this.options
    }
  },
  props: {
    completed: {
      type: Number,
      default: 0
    },
    options: {
      type: Array,
      default() {
        return []
      },
    },
    marginStyle: {
      type: String,
      default: '0'
    },
  },
  directives: {
    // 订单状态颜色指令
    index: function(el, binding) {
      let color = ''
      let res = ""
      if(binding.value < vm.active) {
        res = '0'
      } else if(binding.value == vm.active) {
        res = '1'
      } else {
        res = '2'
      }
      switch (res) {
        case '0':
          color = '#32B9BC'
          break;
        case '1':
          color = '#EB2A5C'
          break;
        default:
          color = '#BBBBBB'
      }
      el.style.color = color
    }
  },
  computed: {},
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  watch: {},
  created() {
    vm = this
  },
  mounted() {},
  methods: {},
}
</script>
<style lang="less" scoped>
.main {
  display:flex;
  margin: 0 auto
}
.step {
  display: flex;
}
.text {
  text-align: center;
  line-height: .2rem;
  color: #BBBBBB
}
.line {
  height: .01rem;
  width: .2rem;
  border-top: 1px dashed #cccccc;
}
.icon {
  width: .16rem;
  height: .16rem;
  margin-top: .08rem
}
</style>