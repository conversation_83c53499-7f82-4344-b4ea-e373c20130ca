<template>
  <div class="coupon">
    <van-tabs
      v-model="status"
      @click="changeTabs"
      color="#222222"
      title-active-color="#222222"
      title-inactive-color="#666666"
      line-width="0"
      line-height="0"
      class="tabs"
    >
      <van-tab
        v-for="item in tabList"
        :title="item.name"
        :name="item.value"
        :key="item.value"
      ></van-tab>
    </van-tabs>
    <div class="main">
      <van-empty
        v-show="couponList.length === 0"
        :image="require('../../assets/images/ykb2023/empty.png')"
        description="优惠券列表为空"
      />
      <div v-show="couponList.length != 0">
        <div class="couponList" v-for="(item, index) in couponList" :key="index">
          <div class="coupon_left">
            <p class="coupon_name" :style="{filter: item.status == 'UNUSED' ? 'grayscale(0)' : 'grayscale(1)'}">购药优惠折扣（满18-10）</p>
            <p class="coupon_time" :style="{filter: item.status == 'UNUSED' ? 'grayscale(0)' : 'grayscale(1)'}">有效期：{{ item.effectTime.split(" ")[0] }} ~ {{ item.expireTime.split(" ")[0] }}</p>
            <van-button class="coupon_detail_btn" icon="arrow" icon-position="right" @click="detailShow = true;currentCoupon = item">查看详情</van-button>
          </div>
          <div class="coupon_right" :style="{filter: item.status == 'UNUSED' ? 'grayscale(0)' : 'grayscale(1)'}">
            <p class="coupon_status" v-show="item.status != 'UNUSED'">{{ statusObj[item.status] }}</p>
            <p class="coupon_amount" :style="{'margin-top': item.status == 'UNUSED' ? '.1rem': '.33rem'}">{{ item.amount }}<img src="~images/hzqjf2024/spec.png" /></p>
            <p class="coupon_label">满18元可用</p>
            <van-button v-show="item.status == 'UNUSED'" class="coupon_use_btn" :to="'/health-ah2024-couponDetail?id=' + item.id + '&cardNo=' + item.cardNo + '&rightCode=' + item.rightCode + '&source=' + source">立即使用</van-button>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model="detailShow" class="dialog" position="bottom" closeable>
      <p class="title">优惠券详情</p>
      <p class="status">{{ statusObj[currentCoupon.status] }}</p>
      <div v-if="currentCoupon" style="border-bottom: 1px solid #F4F4F4;padding-bottom: .2rem;margin: 0 .16rem">
        <div class="detail"><p class="detail_title">名称</p><p class="detail_content">购药优惠折扣（满18-10）</p></div>
        <div class="detail"><p class="detail_title">额度</p><p class="detail_content">{{ currentCoupon.amount }}</p></div>
        <div class="detail"><p class="detail_title">有效期</p><p class="detail_content">{{ currentCoupon.effectTime.split(" ")[0] }} ~ {{ currentCoupon.expireTime.split(" ")[0] }}</p></div>
        <div class="detail"><p class="detail_title">领取日期</p><p class="detail_content">{{ currentCoupon.createTime.split(" ")[0] }}</p></div>
        <div class="detail" v-if="currentCoupon.userTime"><p class="detail_title">核销日期</p><p class="detail_content">{{ currentCoupon.userTime.split(" ")[0] }}</p></div>
      </div>
      <p class="rule_title">使用规则</p>
      <p class="rule_content">限权益人本人使用【{{cardInfo.bindName + '/' + cardInfo.bindIdNum}}】</p>
      <div v-if="currentCoupon.couponDesc" style="margin-top: .12rem">
        <p class="rule" v-for="(item, index) in currentCoupon.couponDesc.split(';')" :key="index">{{ item }}</p>
      </div>
    </van-popup>
  </div>
</template>
<script>
import api from '@/api/cityHealth';
import { maskString } from '@/utils/tools'
export default {
  components: {},
  data() {
    return {
      source: this.$route.query.source || '',
      status: "",
      couponList: [],
      detailShow: false,
      currentCoupon: "",
      cardInfo: "",
      tabList: [{
        name: "全部",
        value: ""
      },{
        name: "已核销",
        value: "USED"
      },{
        name: "未核销",
        value: "UNUSED"
      }, {
        name: "已过期",
        value: "EXPR"
      },  {
        name: "未生效",
        value: "NOTSTART"
      }],
      statusObj: {
        'USED': '已核销',
        'UNUSED': '未核销',
        'EXPR': '已过期',
        'NOTSTART': '未生效'
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    this.submitBrand()
    this.queryCardList()
  },
  mounted() {},
  methods: {
    submitBrand() {
      api.submitBrand({
        cardNo: this.$route.query.cardNo || '',
        source: this.source,
        status: this.status
      }).then((res) => {
        this.couponList = res.result;
      })
    },
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList({ cardNo: this.$route.query.cardNo || '' }).then((res) => {
        res.result.forEach((ele) => {
          ele.bindIdNum = maskString(ele.bindIdNum, 3, 4)
        })
        this.cardInfo = res.result[0]
      })
    },
    // 点击切换tab时
    changeTabs(name) {
      this.status = name;
      this.couponList = [];
      this.submitBrand()
    },
  },
}
</script>
<style lang="less" scoped>
.coupon {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient( 360deg, #FFFFFF 0%, #FFF9F6 100%);
  overflow: auto;
  /deep/.van-tabs__nav {
    background: #fff;
  }
  /deep/.van-tabs__wrap--scrollable .van-tabs__nav--complete {
    padding-left: 0px;
    padding-right: 0px;
    .van-tab {
      padding: 0 0.2rem;
      &.van-tab--active {
        color: @primary-color;
      }
    }
  }
  /deep/.van-tabs--line .van-tabs__wrap {
    height: .56rem
  }
  /deep/.van-tab { 
    font-size: .16rem;
  }
  /deep/.van-tab--active {  
    font-size: .2rem;
    z-index: 100;
    .van-tab__text--ellipsis {
      position: relative;
      z-index: 1;
      line-height: .24rem;
    }
    .van-tab__text::after {
      content: "";
      position: absolute;
      z-index: -1;
      left:0;
      bottom: 0;
      width: 100%;
      height: .08rem;
      background: linear-gradient( 270deg, #FFFFFF 0%, #F98E22 100%)
    }
  }
  .tabs {
    width: 3.75rem;
    height: .56rem;
  }
  .main {
    flex: 1;
    overflow: auto;
    width: 100%;
    // border-radius: .24rem .24rem 0 0;
    // background: rgba(255,255,255,0.5);
    // box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    padding: .11rem 0;
    .couponList {
      background: url('../../assets/images/ykb2023/coupon_bg.png') no-repeat;
      background-size: 100%;
      width: 3.57rem;
      height: 1.24rem;
      margin: 0 auto .06rem;
      display: flex;
      .coupon_left {
        flex: 1;
        margin: .22rem .2rem;
        .coupon_name {
          font-size: .16rem;
          line-height: .22rem;
          color: #222222;
          font-weight: 500;
        }
        .coupon_time {
          font-size: .12rem;
          font-weight: 400;
          color: #666666;
          line-height: .18rem;
          margin-top: .12rem;
        }
        .coupon_detail_btn {
          width: auto;
          height: auto;
          padding: 0;
          border: none;
          font-size: .12rem;
          color: #999999;
          line-height: .18rem;
          margin-top: .12rem;
          /deep/.van-button__icon {
            font-size: 1em
          }
        }
      }
      .coupon_right {
        width: 1.2rem;
        position: relative;
        .coupon_status {
          color: #fff;
          font-size: .14rem;
          line-height: .2rem;
          padding: .04rem .12rem;
          background: linear-gradient(270deg, #A8AAAF 0%, #84868B 100%);
          border-radius: 0 0 0 .16rem;
          position: absolute;
          top: .02rem;
          right: .03rem;
        }
        .coupon_amount {
          font-size: .36rem;
          color: #FD5710;
          line-height: .4rem;
          text-align: center;
          position: relative;
          img {
            width: .2rem;
            position: absolute;
            z-index: 10;
            bottom: 0;
            right: 0.28rem;
          }
        }
        .coupon_label {
          font-size: .12rem;
          color: #999999;
          line-height: .18rem;
          text-align: center;
        }
        .coupon_use_btn {
          width: .8rem;
          height: .32rem;
          padding: 0;
          background: linear-gradient( 180deg, #FC9752 0%, #F75923 100%);
          box-shadow: 0px 5px 10px 0px rgba(253,87,16,0.2), inset 0px -2px 4px 0px rgba(255,165,141,0.4), inset 0px 2px 4px 0px rgba(253,87,16,0.5);
          border-radius: .16rem;
          font-size: .14rem;
          font-weight: 500;
          color: #FFFFFF;
          margin: 0.04rem auto 0;
          display: block;
        }
      }
    }
  }
  .dialog {
    width: 100%;
    // height: 5.12rem;
    padding-bottom: .22rem;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.04);
    border-radius: .24rem .24rem 0 0;
    .title {
      margin: .2rem auto .16rem;
      text-align: center;
      font-size: .2rem;
      font-weight: bold;
      line-height: .32rem
    }
    /deep/.van-popup__close-icon--top-right {
      top: .24rem
    }
    .status {
      font-size: .18rem;
      font-weight: 500;
      color: #FD5710;
      line-height: .24rem;
      margin: .16rem .16rem 0;
    }
    .detail {
      display: flex;
      align-items: center;
      margin: .12rem 0 0;
      .detail_title {
        width: .76rem;
        font-size: .14rem;
        color: #666666;
        line-height: .2rem;
      }
      .detail_content {
        font-size: .14rem;
        color: #222222;
        line-height: .2rem;
        flex: 1;
      }
    }
    .rule_title {
      font-size: .16rem;
      font-weight: 500;
      color: #222222;
      line-height: .22rem;
      margin: .2rem .16rem 0
    }
    .rule_content {
      font-size: .14rem;
      color: #F51A1A;
      line-height: .21rem;
      margin: .12rem .16rem 0;
    }
    .rule {
      font-size: .14rem;
      color: #666666;
      line-height: .21rem;
      margin: 0 .16rem 0.06rem
    }
  }
  /deep/.van-empty {
    margin-top: 1rem;
    .van-empty__image {
      width: 2.5rem;
      height: 1.8rem;
      display: flex;
    }
    .van-empty__description {
      margin-top: 0.24rem;
      font-size: .14rem;
      color: #999999;
    }
  }
}
</style>
