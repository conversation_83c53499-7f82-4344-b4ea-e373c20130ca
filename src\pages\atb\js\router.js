const router = [{
  name: 'health-atb-holder',
  path: '/health-atb-holder',
  component: () => import( /* webpackChunkName: "health-atb-holder" */ '@/pages/atb/holder'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-activation',
  path: '/health-atb-activation',
  component: () => import( /* webpackChunkName: "health-atb-activation" */ '@/pages/atb/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-origin',
  path: '/health-atb-origin',
  component: () => import( /* webpackChunkName: "health-atb-origin" */ '@/pages/atb/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-introduction',
  path: '/health-atb-introduction',
  component: () => import( /* webpackChunkName: "health-atb-introduction" */ '@/pages/atb/introduction'),
  meta: {
    title: '早癌筛查折扣福利',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-introTRT',
  path: '/health-atb-introTRT',
  component: () => import( /* webpackChunkName: "health-atb-introTRT" */ '@/pages/atb/introTRT'),
  meta: {
    title: '中医药专属福利',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-reserve',
  path: '/health-atb-reserve',
  component: () => import( /* webpackChunkName: "health-atb-reserve" */ '@/pages/atb/reserve'),
  meta: {
    title: '权益预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-arrange',
  path: '/health-atb-arrange',
  component: () => import( /* webpackChunkName: "health-atb-arrange" */ '@/pages/atb/arrange'),
  meta: {
    title: '权益预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-success',
  path: '/health-atb-success',
  component: () => import( /* webpackChunkName: "health-atb-success" */ '@/pages/atb/success'),
  meta: {
    title: '权益预约',
    cached: false, // 页面是否要缓存
    wxAuth: false, // 是否需求微信授权
  },
}, {
  name: 'health-atb-supplement',
  path: '/health-atb-supplement',
  component: () => import( /* webpackChunkName: "health-atb-supplement" */ '@/pages/atb/supplement'),
  meta: {
    title: '修改预约',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-atb-supplementSuccess',
  path: '/health-atb-supplementSuccess',
  component: () => import( /* webpackChunkName: "health-atb-supplementSuccess" */ '@/pages/atb/supplementSuccess'),
  meta: {
    title: '修改成功',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}];
export default router;