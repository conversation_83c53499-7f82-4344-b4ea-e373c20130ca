<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon / stroke / time</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="权益预约-预约申请03-选择机构" transform="translate(-32.000000, -631.000000)">
            <g id="弹窗" transform="translate(0.000000, 360.000000)">
                <g id="list" transform="translate(16.000000, 208.000000)">
                    <g id="icon-/-stroke-/-time" transform="translate(16.000000, 63.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <g id="历史" transform="translate(2.000000, 2.000000)" fill="#666666" fill-rule="nonzero">
                            <path d="M5.99997321,0 C9.3136875,0 12,2.68625893 12,5.99997321 C12,9.3136875 9.3136875,12 5.99997321,12 C2.68625893,12 0,9.3136875 0,5.99997321 C0,2.68625893 2.68625893,0 5.99997321,0 Z M8.01254464,1.28748214 C5.40563839,0.186388393 2.39971875,1.40708036 1.298625,4.01398661 C0.19753125,6.62089286 1.41822321,9.6268125 4.02512946,10.7279063 C6.63203571,11.829 9.63795536,10.6082946 10.7390491,8.00140179 C10.8701939,7.69059824 10.9704402,7.36764519 11.0383393,7.03720982 C11.5291071,4.64442857 10.2626652,2.23787946 8.01254464,1.28748214 Z M5.99117411,2.14487946 C6.22763839,2.14487946 6.41934375,2.33657143 6.41934375,2.57304911 L6.41934375,5.82105804 L8.86694196,8.26866964 C9.03415179,8.43587946 9.03415179,8.70699107 8.86694196,8.87420089 C8.69973214,9.04141071 8.42862054,9.04141071 8.26141071,8.87420089 L5.56300446,6.17579464 L5.56300446,2.57304911 C5.56300446,2.33658482 5.75470982,2.14487946 5.99117411,2.14487946 Z" id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>