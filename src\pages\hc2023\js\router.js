const router = [{
  name: 'health-hc2023-activation',
  path: '/health-hc2023-activation',
  component: () => import( /* webpackChunkName: "health-hc2023-activation" */ '@/pages/hc2023/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-hc2023-origin',
  path: '/health-hc2023-origin',
  component: () => import( /* webpackChunkName: "health-hc2023-origin" */ '@/pages/hc2023/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-hc2023-introduction',
  path: '/health-hc2023-introduction',
  component: () => import( /* webpackChunkName: "health-hc2023-introduction" */ '@/pages/hc2023/introduction'),
  meta: {
    title: '权益介绍',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
},];
export default router;