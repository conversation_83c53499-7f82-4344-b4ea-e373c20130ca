<template>
  <div class='reserve'>
    <div style="flex: 1;overflow-y: auto;" class="roll">
      <!-- 步进器 -->
      <atb-step :options="stepList" :stepNum="stepIndex"></atb-step>
      <!-- 权益名称 -->
      <div class="rights_box">
        <div class="title">
          <div class="tit-con">
            <i :style="{ background: '#EB2A5C' }"></i>
            <span>预约权益</span>
          </div>
        </div>
        <div class="rightCode">
          <van-field style="border: 1px solid #dcdcdc" v-model="rightName" readonly placeholder="请选择预约的权益"
            :disabled="true" />
        </div>
        <p v-show="cardInfo && maxUse > 0" class="rightCount" style="color: #EB2A5C">
          *当前权益剩余{{ maxUse - usedCount }}次/共{{ maxUse }}次
        </p>
      </div>
      <!-- 权益人信息 -->
      <div class="rights_box">
        <div class="title">
          <div class="tit-con">
            <i style="background: #EB2A5C"></i>
            <span>权益人信息</span>
          </div>
        </div>
        <van-form ref="personInfo">
          <div class="members">
            <van-field v-model="person" label="权益人" placeholder="请选择权益人" @click="personShow = true" readonly required right-icon="arrow-down" />
          </div>
          <div style="border-bottom: 1px solid #f4f4f4" v-show="!bookingValidation || !bookingValidation.noIdNumPic">
            <p class="upload_title active">请上传权益人证件照片</p>
            <atb-uploader style="display: inline-block; vertical-align: middle" v-bind:fieldName="'IdCardFront'" upLoadText="证件照正面"
              v-bind:OCRIdShow="cardInfo.members && cardInfo.members[this.personIndex].bindIdType == 1" v-bind:bizType="'2'" v-model="params.idCardFront"
              :filePath="idCardFrontPath" :maxCount="1" @getIdCardInfo="getIdCardInfo1" />
            <atb-uploader style="display: inline-block; vertical-align: middle" v-bind:fieldName="'IdCardBack'" upLoadText="证件照反面"
              v-bind:OCRIdShow="cardInfo.members && cardInfo.members[this.personIndex].bindIdType == 1" v-bind:bizType="'2'" v-model="params.idCardBack"
              :filePath="idCardBackPath" :maxCount="1" @getIdCardInfo="getIdCardInfo2" />
            <div style="margin-bottom: 0.12rem">
              <div class="case" @click="bigShow('idCard-front')">
                <img src="~images/common/idCard-front.png" />
                <div>照片示例</div>
              </div>
              <div class="case" @click="bigShow('idCard-back')">
                <img src="~images/common/idCard-back.png" />
                <div>照片示例</div>
              </div>
            </div>
          </div>
          <atb-field-select label="性别" placeholder="请选择性别" v-model="params.gender" :options="sexList"
            optionsTitle="请选择性别" :rules="validateRules.sexRules" required style="border-bottom: 1px solid #f4f4f4" />
          <cx-field-datetime label="出生日期" placeholder="请选择出生日期" v-model="params.birthday" :minDate="minDate"
            :maxDate="maxDate" :rules="validateRules.birthdayRules" :required="true"
            style="border-bottom: 1px solid #f4f4f4" />
          <van-field v-model="params.phoneNo" label="手机号" placeholder="请输入手机号" clearable :maxlength="11" required
            :rules="validateRules.phoneNoRules" />
          <atb-field-area v-show="!bookingValidation || !bookingValidation.noCity" label="所在城市" placeholder="请选择您的所在城市" pompTitle="请选择您的所在城市" v-model="params.city"
            :required="true" />
          <div v-show="bookingValidation && !bookingValidation.noHeight" class="special" style="border-bottom: 1px solid #f4f4f4">
            <van-field type="number" v-model="params.height" label="身高" placeholder="请输入身高" :maxlength="3" required
              :rules="params.height ? validateRules.textRules : []">
              <template #extra><span style="color: #999999">cm</span></template>
            </van-field>
          </div>
          <div v-show="bookingValidation && !bookingValidation.noWeight" class="special">
            <van-field type="number" v-model="params.weight" label="体重" placeholder="请输入体重" :maxlength="3" required
              :rules="params.weight ? validateRules.textRules : []">
              <template #extra><span style="color: #999999">kg</span></template>
            </van-field>
          </div>
        </van-form>
      </div>
      <!-- 预约就诊信息 -->
      <div class="rights_box" v-if="bookingValidation && bookingValidation.appTreatment">
        <div class="title">
          <div class="tit-con">
            <i style="background: #EB2A5C"></i>
            <span>预约就诊信息</span>
          </div>
        </div>
        <van-form ref="treatInfo">
          <atb-casField v-show="bookingValidation.appTreatment.hospitalName" v-model="params.hospitalName"
            :hospitalDistrict="params.hospitalDistrict" @chooseArea="chooseArea"
            style="border-bottom: 1px solid #f4f4f4" />
          <div v-show="bookingValidation.appTreatment.hospitalDept" class="special"
            style="border-bottom: 1px solid #f4f4f4">
            <van-field v-model="params.hospitalDept" label="就诊科室" placeholder="请输入就诊科室名称" clearable required />
          </div>
          <cx-field-datetime v-if="bookingValidation.appTreatment.mediTime" label="就诊时间" placeholder="请选择就诊时间" v-model="params.mediTime" :minDate="mediMinDate"
            :maxDate="mediMaxDate" :rules="validateRules.chooseRules" :required="true"
            style="border-bottom: 1px solid #f4f4f4" />
          <van-field v-model="params.mediDesc" :label="'需求描述/' + '\n' + '病情描述'"
            placeholder="输入详细的病情描述，当前用药及治疗方案、和您的需求等 " type="textarea" maxlength="100" show-word-limit required :rules="validateRules.textRules" />
        </van-form>
      </div>
      <!-- 医学证明材料 -->
      <div class="rights_box" v-if="bookingValidation && bookingValidation.mediFiles">
        <div class="title">
          <div class="tit-con">
            <i style="background: #EB2A5C"></i>
            <span>医学证明材料</span>
          </div>
        </div>
        <van-form ref="fileInfo">
          <div style="border-bottom: 1px solid #f4f4f4" v-show="bookingValidation.mediFiles.mediRecords">
            <p class="upload_title">既往门/急诊及住院病历</p>
            <atb-uploader v-bind:fieldName="'MediRecords'" v-bind:bizType="'2'" v-model="params.mediRecords" />
          </div>
          <div style="border-bottom: 1px solid #f4f4f4" v-show="bookingValidation.mediFiles.diagReports">
            <p class="upload_title">诊断报告</p>
            <atb-uploader v-bind:fieldName="'DiagReports'" v-bind:bizType="'2'" v-model="params.diagReports" />
          </div>
          <div style="border-bottom: 1px solid #f4f4f4" v-show="bookingValidation.mediFiles.inspReports">
            <p class="upload_title">影像报告与检验报告（半年内）</p>
            <atb-uploader v-bind:fieldName="'InspReports'" v-bind:bizType="'2'" v-model="params.inspReports" />
          </div>  
          <div style="border-bottom: 1px solid #f4f4f4" v-show="bookingValidation.mediFiles.hospital">
            <p class="upload_title">住院依据（住院单或其他证明文件）</p>
            <atb-uploader v-bind:fieldName="'Hospital'" v-bind:bizType="'2'" v-model="params.hospital" />
          </div>
          <div v-show="bookingValidation.mediFiles.others">
            <p class="upload_title">其他</p>
            <atb-uploader v-bind:fieldName="'Others'" v-bind:bizType="'2'" v-model="params.others" />
          </div>
        </van-form>
      </div>
    </div>

    <!-- 下一步 -->
    <div class="next">
      <van-button type="primary" class="btn" @click="next" style="background: linear-gradient(270deg, #FD7570 0%, #ED5075 100%);border: none">提交</van-button>
    </div>

    <!-- 样张弹窗 -->
    <van-popup v-model="caseShow" style="background: transparent">
      <img :src="require(`../../assets/images/common/${casePath}.png`)" style="width: 2.75rem" />
    </van-popup>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import { theme, userInfoDict } from '@/mixin'
// import { maleOrFemalByIdCard, birthdayByIdCard } from '@/utils/tools'
import validateRules from '@/utils/fieldValidateRules'
import atbStep from './components/step.vue'
import atbUploader from './components/uploader.vue'
import atbFieldSelect from './components/fieldSelect'
import atbFieldArea from './components/fieldArea.vue'
import atbCasField from './components/casField.vue'
export default {
  mixins: [theme, userInfoDict],
  components: { atbStep, atbUploader, atbFieldSelect, atbFieldArea, atbCasField },
  data() {
    return {
      params: { // 预约提交表单
        cardNo: this.$route.query.cardNo || "", // 卡号
        rightCode: this.$route.query.rightCode || "", // 权益code
        gender: "", // 性别
        birthday: "", // 出生日期
        phoneNo: "", // 手机号
        city: "", // 所在城市
        height: "", // 升高
        weight: "", // 体重
        idCardFront: [],
        idCardBack: [],
        hospitalDistrict: '', // 就诊地区
        hospitalName: '', // 就诊医院
        hospitalDept: '', // 就诊科室
        mediTime: '', // 就诊时间
        mediDesc: '', // 需求描述/病情描述
        rescueDistrict: "", //120医疗救护地区
        rescueAddress: "", // 120医疗救护详细地址
        rescueHospital: "", // 120医疗救护就诊医院
        rescueReceipt: [], // 120医疗救护材料
        nurseDistrict: "", // 院后照护地区
        nurseAddress: "", // 院后照护详细地址
        mediRecords: [], // 既往门/急诊及住院病历
        diagReports: [], // 诊断报告
        inspReports: [], // 影像报告与检验报告（半年内）
        hospital: [], // 住院依据（住院单或其他证明文件）
        others: [], // 其他
        bindName: "",
        bindIdNum: ""
      },
      cardInfo: {}, // 卡信息
      rightsList: [], // 权益信息
      person: "", // 当前选择权益人姓名证件号
      personIndex: 0,
      personShow: false, // 选择权益人弹窗
      minDate: new Date(1900, 0, 1), // 最小可选日期
      maxDate: new Date(), // 最大可选日期
      idCardFrontPath: [], // 证件照
      idCardBackPath: [], // 证件照
      caseShow: false, // 样张弹窗
      casePath: 'idCard-front', // 样张名
      OCRSuccess1: false, // OCR校验通过
      OCRSuccess2: false, // OCR校验通过
      stepList: [
        {
          name: '预约告知',
        },
        {
          name: '预约申请',
        },
        {
          name: '预约安排',
        },
        {
          name: '预约完成',
        },
      ],
      stepIndex: '2', // 步进器索引值
      validateRules, // 校验规则
    };
  },
  computed: {
    rightName() {
      return this.rightsList.length > 0 ? this.rightsList[0].rightName : ""
    },
    maxUse() {
      return this.rightsList.length > 0 ? this.rightsList[0].maxUse : 0
    },
    usedCount() {
      return this.rightsList.length > 0 ? this.rightsList[0].usedCount : 0
    },
    bookingValidation() {
      return this.rightsList.length > 0 ? this.rightsList[0].bookingValidation : ""
    },
    mediMinDate() {
      var a = new Date()
      return new Date(a.getTime() + 24 * 60 * 60 * 1000)
    },
    mediMaxDate() {
      var date = new Date()
      date.setFullYear(date.getFullYear() + 1)
      date.setDate(date.getDate() - 1)
      return date
    },
  },
  watch: {
    // 'params.bindIdNum': function (idNum) {
    //   console.log(idNum)
    //   if (this.cardInfo.members[this.personIndex].bindIdType == 1) {
    //     this.params.birthday = birthdayByIdCard(idNum)
    //     this.params.gender = maleOrFemalByIdCard(idNum)
    //   }
    // }
  },
  created() {
    // 查询卡信息
    this.queryOrderDetail()
  },
  mounted() {},
  methods: {
    queryOrderDetail() {
      api.queryOrderDetail({ id: this.$route.query.id }).then((res) => {
        this.params.rightCode = res.result.rightCode
        this.params.gender = res.result.gender
        this.params.birthday = res.result.birthday
        if(this.params.birthday&&this.params.birthday.length>0){
          this.birthdayDisabled=true;
        }
        if(this.params.gender&&this.params.gender.length>0){
          this.genderDisabled=true;
        }
        this.params.phoneNo = res.result.phoneNo
        this.params.city = res.result.city
        this.params.height = res.result.height
        this.params.weight = res.result.weight
        this.params.hospitalDept = res.result.hospitalDept
        this.params.mediTime = res.result.mediTime && res.result.mediTime.split(' ')[0]
        this.params.mediDesc = res.result.mediDesc
        // 证件照
        this.idCardFrontPath = res.result.files.filter((item) => item.fieldName =='IdCardFront')
        this.params.idCardFront.push(this.idCardFrontPath[0].id)
        this.idCardBackPath = res.result.files.filter((item) => item.fieldName =='IdCardBack')
        this.params.idCardBack.push(this.idCardBackPath[0].id)
        this.OCRSuccess1 = true
        this.OCRSuccess2 = true
        this.params.rescueDistrict = res.result.rescueDistrict
        this.params.rescueHospital = res.result.rescueHospital
        this.params.rescueAddress = res.result.rescueAddress
        this.params.nurseDistrict = res.result.nurseDistrict
        this.params.nurseAddress = res.result.nurseAddress
        setTimeout(() => {
          this.params.hospitalDistrict = res.result.hospitalDistrict
          this.params.hospitalName = res.result.hospitalName
          // 材料
          this.mediRecordsPath = res.result.files.filter((item) => item.fieldName == 'MediRecords')
          this.diagReportsPath = res.result.files.filter((item) => item.fieldName == 'DiagReports')
          this.inspReportsPath = res.result.files.filter((item) => item.fieldName == 'InspReports')
          this.hospitalPath = res.result.files.filter((item) => item.fieldName == 'Hospital')
          this.othersPath = res.result.files.filter((item) => item.fieldName == 'Others')
          this.rescuePath = res.result.files.filter((item) => item.fieldName == 'RescueReceipt')
          res.result.files.forEach((ele) => {
            switch (ele.fieldName) {
              case 'MediRecords':
                this.params.mediRecords.push(ele.id)
                break
              case 'DiagReports':
                this.params.diagReports.push(ele.id)
                break
              case 'InspReports':
                this.params.inspReports.push(ele.id)
                break
              case 'Hospital':
                this.params.hospital.push(ele.id)
                break
              case 'Others':
                this.params.others.push(ele.id)
                break
              case 'RescueReceipt':
                this.params.rescueReceipt.push(ele.id)
            }
          })
        }, 1500);
        // 查询权益信息
        this.getRights(res.result.bindIdNum)
      })
    },
    // 查询权益
    getRights(idNum) {
      api.pbmCardRights({ cardNo: this.params.cardNo }).then((res) => {
        this.rightsList = res.result.filter((item) => item.rightCode == this.params.rightCode)
        this.getCardInfo(idNum)
      })
    },
    // 查询卡信息
    getCardInfo(idNum) {
      api.queryCardList({ cardNo: this.params.cardNo }).then((res) => {
        this.cardInfo = res.result[0]
        this.personIndex = this.cardInfo.members.findIndex(item => item.bindIdNum === idNum)
        this.personConfirm()
      })
    },
    // 下一步
    next() {
      if((!this.OCRSuccess1 || !this.OCRSuccess2 || this.params.idCardFront.length == 0 || this.params.idCardBack.length == 0) 
        && !this.bookingValidation.noIdNumPic) {
        this.$toast("请上传正确的证件照正反面")
        this.$el.querySelector(".roll").scrollTop = this.$refs.personInfo.$el.offsetTop
        return
      }
      // 全表单校验
      Promise.all([
        this.$refs.personInfo.validate(),
        this.bookingValidation && this.bookingValidation.appTreatment ? this.$refs.treatInfo.validate() : "",
        this.bookingValidation && this.bookingValidation.rescue ? this.$refs.rescue.validate() : "",
        this.bookingValidation && this.bookingValidation.affHospCare ? this.$refs.affHospCare.validate() : ""
      ]).then(() => {
        if (this.params.birthday.split(' ').length == '1') {
          this.params.birthday = this.params.birthday + ' 00:00:00'
        }
        if (this.params.mediTime && this.params.mediTime.split(' ').length == '1') {
          this.params.mediTime = this.params.mediTime + ' 00:00:00'
        }
        this.params.idCardFront = this.params.idCardFront[0]
        this.params.idCardBack = this.params.idCardBack[0]
        this.params.height = Number(this.params.height)
        this.params.weight = Number(this.params.weight)
        this.params.id = this.$route.query.id
        api.update(this.params).then(() => {
          this.$router.replace('/health-atb2023-supplementSuccess')
        })
      }).catch(() => {
        this.$toast("请填写正确的信息")
      })
    },
    // 选择就诊地区
    chooseArea(val) {
      this.params.hospitalDistrict = val
    },
    personConfirm() {
      this.person = this.cardInfo.members[this.personIndex].bindName + "/" + this.cardInfo.members[this.personIndex].bindIdNum;
      this.params.bindName = this.cardInfo.members[this.personIndex].bindName
      this.params.bindIdNum = this.cardInfo.members[this.personIndex].bindIdNum
    },
    // 大图显示
    bigShow(path) {
      this.caseShow = true
      this.casePath = path
    },
    // OCR识别
    getIdCardInfo1(event) {
      if(this.cardInfo.members[this.personIndex].bindIdType == 1) {
        this.OCRSuccess1 = event.name === this.params.bindName && event.idNum === this.params.bindIdNum
      } else {
        this.OCRSuccess1 = true
      }
    },
    getIdCardInfo2(event) {
      if(this.cardInfo.members[this.personIndex].bindIdType == 1) {
        this.OCRSuccess2 = event.authority ? true : false
      } else {
        this.OCRSuccess2 = true
      }
    }
  },
}
</script>
<style lang="less" scoped>
@import url('../healthService/reserve.less');
</style>