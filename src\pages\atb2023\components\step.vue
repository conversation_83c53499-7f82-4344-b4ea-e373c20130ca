<template>
  <div class="minipro">
    <div v-for="(item, index) in stepList" :key="index" class="stepList">
      <p :class="active >= index + 1 ? 'active_num' : 'num'" :style="{background: active >= index + 1 ? bgColor : '#fff'}">{{ index + 1 }}</p>
      <p :class="active >= index + 1 ? 'active_name' : 'name'" :style="{color: active >= index + 1 ? color : '#cccccc'}">{{ item.name }}</p>
      <img
        v-show="index < stepList.length - 1"
        :src="
          active >= index + 1
            ? require('../../../assets/images/hxb/step_line.png')
            : require('../../../assets/images/icon/step_line_unfinished.png')
        "
        class="img"
      />
    </div>
  </div>
</template>
<script>
import { theme } from '@/mixin'
export default {
  mixins: [theme],
  name: '',
  model: {
    prop: 'value',
    event: 'change',
  },
  components: {},
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    stepNum: {
      type: String,
      default: '0',
    },
    options: {
      type: Array,
      default() {
        return []
      },
    },
    bgColor: {
      type: String,
      default: 'linear-gradient(270deg, #FD7570 0%, #ED5075 100%)'
    },
    color: {
      type: String,
      default: '#EB2A5C'
    }
  },
  data() {
    return {
      stepList: this.options,
      active: this.stepNum,
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
}
</script>
<style lang="less" scoped>
.minipro {
  display: flex;
  justify-content: center;
  // align-items: start;
  margin: 0.2rem auto;
}
.stepList {
  flex: 1;
  position: relative;
}
.num {
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
  color: #cccccc;
  font-size: 0.16rem;
  font-weight: 500;
  text-align: center;
  line-height: 0.3rem;
  margin: 0 auto;
  border: 1px solid #cccccc;
}
.active_num {
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
  color: #fff;
  font-size: 0.16rem;
  font-weight: 500;
  text-align: center;
  line-height: 0.3rem;
  margin: 0 auto;
}
.active_name {
  font-size: 0.12rem;
  font-weight: 500;
  margin-top: 0.07rem;
  text-align: center;
  line-height: 0.18rem;
}
.name {
  font-size: 0.12rem;
  font-weight: 500;
  margin-top: 0.07rem;
  text-align: center;
  line-height: 0.18rem;
}
.img {
  width: 0.12rem;
  position: absolute;
  right: -0.06rem;
  top: 0.1rem;
}
</style>