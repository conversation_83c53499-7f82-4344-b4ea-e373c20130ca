<template>
  <div class="areaFiled">
    <div class="area_block" @click="show = true">
      <p :style="{ color: fieldValue == '选择地区' ? '#666666' : '#222222' }">{{ fieldValue }}</p>
      <img src="~images/icon/down.png" alt="" />
    </div>

    <van-field v-model="hospitalName" placeholder="请输入就诊医院名称" clearable />

    <van-popup v-model="show" round position="bottom">
      <van-cascader
        v-model="cascaderValue"
        title="请选择所在地区"
        :options="options"
        :field-names="fieldNames"
        @close="show = false"
        @finish="onFinish"
      />
    </van-popup>
  </div>
</template>
<script>
import { theme } from '@/mixin'
import { areaList } from '../../../components/CxCasField/area.js'
import { Cascader } from 'vant'
export default {
  mixins: [theme],
  model: {
    prop: 'value',
    event: 'change'
  },
  name: '',
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    value: {
      type: String,
      default: ''
    },
    hospitalDistrict: {
      type: String,
      default: ''
    }
  },
  components: {
    'van-cascader': Cascader,
  },
  data() {
    return {
      hospitalName: "",
      show: false,
      fieldValue: '选择地区',
      cascaderValue: '', // 选项列表，children 代表子选项，支持多级嵌套
      fieldNames: {
        text: 'label',
        value: 'value',
        children: 'children',
      },
      options: areaList,
    }
  },
  computed: {},
  watch: {
    hospitalName(val) {
      this.$emit('change', val)
    },
    hospitalDistrict(val) {
      this.fieldValue = val.split('市')[1] || (val.split('区')[1] + '区') || val.split('盟')[1] || val.split('县')[1] || val.split('州')[1] || val.split('區')[1]
    },
    value(val) {
      this.hospitalName = val
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 全部选项选择完毕后，会触发 finish 事件
    onFinish({ selectedOptions }) {
      this.show = false
      this.fieldValue = selectedOptions[2].label
      this.$emit('chooseArea', selectedOptions.map((option) => option.label).join(''))
    },
  },
}
</script>
<style lang="less" scoped>
.areaFiled {
  display: flex;
  align-items: center;
  .area_block {
    width: 1.1rem;
    overflow: hidden;
    padding: 0 0.12rem 0 0;
    margin: 0.16rem 0;
    border-right: 1px solid #dddddd;
    display: flex;
    align-items: center;
    p {
      margin-right: 0.04rem;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    img {
      width: 0.12rem;
    }
  }
  /deep/.van-field {
    padding: 0.16rem !important;
  }
  /deep/.van-cascader__header {
    margin-top: 0.1rem;
  }
  /deep/.van-cascader__title {
    margin: 0 auto;
    font-weight: 500;
  }
  /deep/.van-cascader__tabs.van-tabs--line .van-tabs__wrap {
    height: 0.4rem;
  }
  /deep/.van-tabs__line {
    background: #EB2A5C;
    width: 0.2rem;
  }
  /deep/.van-cascader__tabs .van-tab {
    font-weight: 500;
  }
  /deep/.van-cascader__option--selected {
    color: #EB2A5C;
  }
  /deep/.van-cascader__close-icon {
    color: #222222;
  }
}
</style>