const router = [{
  name: 'health-bs2024-activation',
  path: '/health-bs2024-activation',
  component: () => import( /* webpackChunkName: "health-bs2024-activation" */ '@/pages/bs2024/activation'),
  meta: {
    title: '权益激活',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-bs2024-origin',
  path: '/health-bs2024-origin',
  component: () => import( /* webpackChunkName: "health-bs2024-origin" */ '@/pages/bs2024/origin'),
  meta: {
    title: '我的权益',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
}, {
  name: 'health-bs2024-introduction',
  path: '/health-bs2024-introduction',
  component: () => import( /* webpackChunkName: "health-bs2024-introduction" */ '@/pages/bs2024/introduction'),
  meta: {
    title: '权益介绍',
    cached: false, // 页面是否要缓存
    wxAuth: true, // 是否需求微信授权
  },
},];
export default router;