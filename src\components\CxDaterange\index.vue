<template>
  <van-popup v-model="popupShow" @click-overlay="handleCancel" position="bottom">
    <!-- 顶部栏 -->
    <div v-if="showToolbar" class="van-picker__toolbar">
      <button
        type="button"
        @click="handleCancel"
        class="van-picker__cancel"
      >
        {{cancelButtonText}}
      </button>
      <div class="van-ellipsis van-picker__title">{{title}}</div>
      <button
        type="button"
        @click="handleConfirm"
        class="van-picker__confirm"
      >
        {{confirmButtonText}}
      </button>
    </div>

    <!-- 日期 -->
    <div class="date-input">
      <input
        :value="startDateStr"
        :class="{active: currentField === 'startDate'}"
        type="text"
        readonly="readonly"
        :placeholder="startPlaceholder"
        @click="selectField('startDate')" 
      />
      <span>{{rangeSeparator}}</span>
      <input
        :value="endDateStr"
        :class="{active: currentField === 'endDate'}"
        type="text"
        readonly="readonly"
        :placeholder="endPlaceholder"
        @click="selectField('endDate')"
      />
    </div>

    <van-datetime-picker
      v-bind="dateProps"
      v-model="currentDate"
      :min-date="minDate"
      :max-date="maxDate"
      :show-toolbar="false"
      @change="handleChange"
    />
  </van-popup>
</template>

<script>
export default {
  name: 'CxDaterange',
  model: {
    prop: 'value',
    event: 'confirm'
  },
  inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
  props: {
    // 是否显示
    show: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 返回值是否需要格式化
    // isFormatDate: {
    //   type: Boolean,
    //   default: true
    // },
    // 选择范围时的分隔符
    rangeSeparator: {
      type: String,
      default: '-'
    },
    // 日期分隔符
    dateSeparator: {
      type: String,
      default: '/'
    },
    startPlaceholder: {
      type: String,
      default: '开始日期'
    },
    endPlaceholder: {
      type: String,
      default: '结束日期'
    },
    // 最小可选开始日期
    minStartDate: {
      type: Date
    },
    // 最大可选开始日期
    maxStartDate: {
      type: Date
    },
    // 最小可选结束日期
    minEndDate: {
      type: Date
    },
    // 最大可选结束日期
    maxEndDate: {
      type: Date
    },
    // 是否显示顶部栏
    showToolbar: {
      type: Boolean,
      default: true
    },
    // 顶部栏标题
    title: {
      type: String,
      default: '选择时间'
    },
    // 确认按钮文字
    confirmButtonText: {
      type: String,
      default: '确认'
    },
    // 取消按钮文字
    cancelButtonText: {
      type: String,
      default: '取消'
    }
  },
  data() {
    return {
      popupShow: false,
      startDate: new Date(), // 开始日期
      endDate: new Date(), // 结束日期
      currentField: 'startDate',
      currentDate: new Date(), // van-datetime-picker 组件绑定的值
      minDate: new Date(1900, 0, 1), // 当前项最小时间（可能是开始时间，也可能是结束时间）
      maxDate: new Date(), // 当前项最大时间（可能是开始时间，也可能是结束时间）
    }
  },
  computed: {
    // 合并后的 van-datetime-picker 组件属性（默认为date类型）
    dateProps() {
      return {
        type: 'date',
        ...this.$attrs
      }
    },
    // 格式化后的时间字符串
    startDateStr() {
      return this.formatDate(this.startDate)
    },
    // 格式化后的时间字符串
    endDateStr() {
      return this.formatDate(this.endDate)
    },
    // 当前项最小时间（可能是开始时间，也可能是结束时间）
    // minDate() {
    //   return this.currentField === 'endDate' ? this.minEndDate : this.minStartDate
    // },
    // 当前项最大时间（可能是开始时间，也可能是结束时间）
    // maxDate() {
    //   return this.currentField === 'endDate' ? this.maxEndDate : this.maxStartDate
    // },
  },
  watch: {
    show(value) {
      this.popupShow = value
    },
    value: {
      deep: true,
      immediate: true,
      handler(arr) {
        this.computeOfValue(arr)
      }
    },
    minStartDate: {
      deep: true,
      immediate: true,
      handler() {
        this.setMaxMinDate()
      }
    },
    maxStartDate: {
      deep: true,
      immediate: true,
      handler() {
        this.setMaxMinDate()
      }
    },
    minEndDate: {
      deep: true,
      immediate: true,
      handler() {
        this.setMaxMinDate()
      }
    },
    maxEndDate: {
      deep: true,
      immediate: true,
      handler() {
        this.setMaxMinDate()
      }
    }
  },
  methods: {
    // 设置最大最小值
    setMaxMinDate() {
      this.minDate = this.currentField === 'endDate' ? this.minEndDate : this.minStartDate
      this.maxDate = this.currentField === 'endDate' ? this.maxEndDate : this.maxStartDate
    },
    // 选择字段
    selectField(field) {
      this.currentField = field
      this.setMaxMinDate()
      this.currentDate = this[this.currentField]
    },
    handleChange() {
      // if(this.currentField === 'startDate' && this.endDate) {
      //   // const endDate = this.endDate.replace(/\D/g, '/')
      //   if (this.currentDate.getTime() > this.endDate.getTime()) {
      //     this.$toast('开始日期不能大于结束日期')
      //     return
      //   }
      // }
      // if(this.currentField === 'endDate' && this.startDate) {
      //   // const startDate = this.startDate.replace(/\D/g, '/')
      //   if (this.currentDate.getTime() < this.startDate.getTime()) {
      //     this.$toast('结束日期不能小于开始日期')
      //     return
      //   }
      // }

      this[this.currentField] = this.currentDate
      const value = [this.startDate, this.endDate]
      this.$emit('change', value)
    },
    // 确认选中日期
    handleConfirm() {
      this.$emit('confirm', [this.startDate, this.endDate])
      this.$emit('update:show', false)
    },
    // 取消
    handleCancel() {
      this.$emit('update:show', false)
    },
    // 根据value计算相应的值
    computeOfValue(arr) {
      if (arr && arr.length === 2) {
        // this.startDate = arr[0] ? arr[0].replace(/\D/g, this.dateSeparator) : '' // 开始日期
        // this.endDate =  arr[1] ? arr[1].replace(/\D/g, this.dateSeparator) : '' // 结束日期
        // // 把日期分隔符替换成'/'是为了兼容ISO手机
        // this.currentDate = new Date(this[this.currentField].replace(/\D/g, '/'))

        if (Object.prototype.toString.call(arr[0]) !== '[object Date]' || Object.prototype.toString.call(arr[1]) !== '[object Date]') {
          console.error('值数组的每项必须是日期格式')
          return
        }
        
        this.startDate = arr[0] ? new Date(arr[0]) : new Date() // 开始日期
        this.endDate =  arr[1] ? new Date(arr[1]) : new Date() // 结束日期
        this.currentDate = this[this.currentField]
      }
    },
    // 格式化日期数据
    formatDate(dateData) {
      // 不是数据类型返回空
      if (Object.prototype.toString.call(dateData) !== '[object Date]') {
        return ''
      }
      // 时间格式，直接返回
      if (this.dateProps.type === 'time') {
        return dateData
      }

      let year = dateData.getFullYear().toString()        // 年
      let month = (dateData.getMonth() + 1).toString()    // 月
      let date = dateData.getDate().toString()            // 日
      let H = dateData.getHours().toString()              // 时
      let M = dateData.getMinutes().toString()            // 分
      let S = dateData.getSeconds().toString()            // 秒

      // 如果是个数，则前补0
      month = this.beforeFill0(month)
      date = this.beforeFill0(date)
      H = this.beforeFill0(H)
      M = this.beforeFill0(M)
      S = this.beforeFill0(S)

      let stringDate = ''

      if (this.dateProps.type === 'datetime') {
        stringDate = `${year}${this.dateSeparator}${month}${this.dateSeparator}${date} ${H}:${M}:${S}`;
      } else if (this.dateProps.type === 'datehour') {
        stringDate = `${year}${this.dateSeparator}${month}${this.dateSeparator}${date} ${H}`;
      } else if (this.dateProps.type === 'date') {
        stringDate = `${year}${this.dateSeparator}${month}${this.dateSeparator}${date}`;
      } else if (this.dateProps.type === 'year-month') {
        stringDate = `${year}${this.dateSeparator}${month}`;
      } else if (this.dateProps.type === 'month-day') {
        stringDate = `${month}${this.dateSeparator}${date}`;
      }

      return stringDate;
    },
    // 前补0方法
    beforeFill0(num) {
      if(num < 10) {
        return '0' + num
      } else {
        return num
      }
    }
  }
}
</script>

<style lang="less" scoped>
.date-input {
  padding: 0.16rem 0.24rem 0.16rem;
  display: flex;

  line-height: 0.35rem;
  input {
    width: 0.8rem;
    height: 0.33rem;
    background: #F5F5F5;
    border-radius: 0.04rem;
    border: 1px solid #F5F5F5;
    outline: none;
    padding: 0 0.17rem;
    flex: 1;
    color: #2846FF;
    font-size: 0.16rem;
    &::placeholder {
      color: #999999;
    }
    &.active {
      border: 1px solid #2846FF;
    }
  }
  span {
    padding: 0 0.09rem;
  }
}

.van-picker__toolbar {
  .van-picker__title {
    color: #484848;
  }
  .van-picker__cancel {
    color: #2F3032;
  }
  .van-picker__confirm {
    color: #2846FF;
  }
}

</style>