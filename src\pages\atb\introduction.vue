<template>
  <div class="introduction">
    <img src="~images/atb/res_part_PR012.png" class="img" />
    <p class="agreement">
      更多权益详见<zcx-agreement-box :isInline="true" :queryParams="agreeUrlQuery" :color="textColor"/>
      <!-- <span @click="agreeChange('1')">《早癌筛查介绍》</span><span @click="agreeChange('2')">《早癌筛查折扣福利使用说明书》</span>。 -->
    </p>

    <div class="reserve_box">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>服务流程</span>
        </div>
      </div>
      <atb-step style="width: 2.87rem" :options="stepList1" :stepNum="'3'"></atb-step>
      <atb-step style="width: 2.87rem" :options="stepList2" :stepNum="'3'"></atb-step>
    </div>

    <div class="reserve_box" style="margin-bottom: 1rem">
      <div class="title">
        <div class="tit-con">
          <i></i>
          <span>在线咨询</span>
        </div>
      </div>
      <img src="~images/common/callCenter-qrCode.png" class="qrCode">
      <p class="content">长按二维码，添加健康服务管家</p>
    </div>

    <div class="footer">
      <van-button class="btn" @click="getPvUv()">立即预约</van-button>
    </div>

    <!-- 协议弹窗 -->
    <zcx-agreement-popup v-model="agreePopup" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <keep-alive>
          <component v-bind:is="currentComponent" class="agreementBox"></component>
        </keep-alive>
      </div>
    </zcx-agreement-popup>
  </div>
</template>
<script>
import api from '@/api/cityHealth'
import AtbStep from './components/AtbStep.vue'
import ZcxAgreementBox from '@/components/ZcxAgreementBox/index'
export default {
  name: '',
  components: { AtbStep, ZcxAgreementBox },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"103",keys:"intro,zkfl"},
      textColor:"#F34168",
      currentName: '', // 协议名称
      currentComponent: '', // 协议内容
      agreePopup: false, // 协议弹窗
      stepList1: [{
        num: '1',
        name: '立即预约',
      }, {
        num: '2',
        name: '填写信息',
      }, {
        num: '3',
        name: '免费风险测试',
      }],
      stepList2: [{
        num: '4',
        name: '专员联络',
      }, {
        num: '5',
        name: '预约筛查时间',
      }, {
        num: '6',
        name: '到院购买和检查',
      }],
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    // console.log(process.env)
  },
  methods: {
    getPvUv() {
      api.getPvUv({ eventOwner: '103', eventCode: 'PR012', eventType: 'RIGHTS'}).then(() => {
        this.$router.push({
          path: "/health-atb-reserve",
          query: {
            cardNo: this.$route.query.cardNo
          }
        })
      })
    },
    agreeChange(index) {
      if(index == '1') {
        this.currentName = '早癌筛查介绍'
        this.currentComponent = 'atb_js'
      } else {
        this.currentName = '早癌筛查折扣福利使用说明书'
        this.currentComponent = 'atb_sysm'
      }
      this.agreePopup = true
    }
  },
}
</script>
<style lang="less" scoped>
.introduction {
  .img {
    width: 100%;
    margin: 0.16rem 0 0;
  }
  .agreement {
    padding: 0 .16rem;
    text-indent: .28rem;
    color: #222222;
    span {
      color: #F34168;
    }
  }
  .reserve_box {
    width: 3.43rem;
    background: #fff;
    border-radius: 0.12rem;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    margin: 0.16rem auto 0;
    padding: 0.16rem 0 .16rem;
    .title {
      font-size: 0.18rem;
      font-weight: 500;
      line-height: 0.24rem;
      display: flex;
      .tit-con {
        flex: 1;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.12rem;
          width: 0.04rem;
          height: 0.2rem;
          background: #F34168;
          border-radius: 0.02rem;
        }
        span {
          vertical-align: middle;
          line-height: 0.26rem;
        }
      }
    }
    .qrCode {
      width: 1.02rem;
      margin: 0 1.2rem
    }
    .content {
      color: #999999;
      font-size: .1rem;
      text-align: center;
      // margin-top: .08rem
    }
  }
  .footer {
    width: 100%;
    height: 0.8rem;
    background: #ffffff;
    position: fixed;
    z-index: 10;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #F4F4F4;
    .btn {
      width: 3.43rem;
      height: 0.48rem;
      background: #F34168;
      border-radius: 0.24rem;
      color: #fff;
      font-size: 0.18rem;
      font-weight: 500;
      border: none
    }
  }
  .readPopup {
    height: 100%;
    display: flex;
    flex-direction: column;
    .title {
      height: 0.6rem;
      box-sizing: border-box;
      line-height: 0.28rem;
      padding: 0.16rem;
      text-align: center;
      font-weight: 400;
      color: #333333;
      font-size: 0.16rem;
      position: relative;
      h2 {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
    }
    .agreementBox {
      flex: 1;
      overflow-y: scroll;
      margin-bottom: 0.1rem;
    }
  }
}
</style>