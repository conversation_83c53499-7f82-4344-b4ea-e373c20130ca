<template>
  <div class="home">
    <img
      :src="src"
      ref="avatar"
      class="avatar"
      :style="{ left: toLeft - botImg + 'px' }"
    />
    <img
      src="./img/avatar-bg.png"
      ref="avatarBg"
      class="avatar-bg"
      :style="{ left: toLeft - topImg + 'px' }"
    />
    <p class="title">这是我加入宸汐的</p>
    <p class="label">第<span class="num">826</span>天</p>
  </div>
</template>
<script>
export default {
  name: '',
  components: {},
  data() {
    return {
      toLeft: '',
      topImg: '',
      botImg: '',
      src: require('./img/avatar.png')
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.topImg = this.$refs.avatarBg.width * 0.5
    this.botImg = this.$refs.avatar.width * 0.5
    this.toLeft = document.documentElement.clientWidth * 0.5
  },
  methods: {},
}
</script>
<style lang="less" scoped>
.home {
  height: 100vh;
  overflow: auto;
  background: url('./img/anni-bg.png') no-repeat;
  background-size: 100%;
  position: relative;
  .avatar {
    border-radius: 50%;
    width: 0.6rem;
    height: 0.6rem;
    overflow: hidden;
    position: absolute;
    z-index: 10;
    top: 1.48rem;
  }
  .avatar-bg {
    width: .86rem;
    height: .86rem;
    position: absolute;
    z-index: 100;
    top: 1.36rem;
  }
  .title {
    font-size: 0.24rem;
    letter-spacing: 2px;
    line-height: 0.24rem;
    text-align: center;
    margin-top: 2.4rem;
  }
  .label {
    text-align: center;
    font-size: 0.24rem;
    margin-top: 0.14rem;
    .num {
      font-size: 0.4rem;
      margin: 0 0.14rem;
      letter-spacing: 1px;
    }
  }
  p {
    color: #fff;
  }
}
</style>
