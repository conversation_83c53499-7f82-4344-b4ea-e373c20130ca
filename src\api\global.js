import http from '../utils/http';

export default {

  /**
   * 通用数据
   */

  // 获取指定类型的字典——数组 (参数：字典类型，字符串)
  queryDictArray: (dictCode, config) => http.get('/api-config/v1/dict/data/dictCode?dictCode=' + dictCode, {}, config),

  // 获取指定类型的字典——对象 (参数：字典类型，字符串)
  queryDictObject: (dictCode, config) => http.get('/api-config/v1/dict/value/dictCode?dictCode=' + dictCode, {}, config),

  // 获取验证码 (参数：phoneNo)
  querySmsCode: (params, config) => http.post('/api/v1/user/registerSmsCode', params, config),

  // 判断是否为网页授权 (参数：state)
  webAuth: (params, config) => http.post('/wx/v1/check', params, config),

  // 微信公众号创建调用jsapi时所需要的签名 (参数：appId, url)
  signature: (params, config) => http.post('/api-wechat/v1/jsapi/signature', params, config),
}
