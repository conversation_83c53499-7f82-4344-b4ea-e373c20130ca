<template>
  <div class="holder">
    <div class="holder_box">
      <div class="banner"></div>
      <!-- 被保人列表 -->
      <div class="insuredList" v-show="list.length > 0">
        <p class="insured-title">您可激活以下被保人的权益：</p>
        <van-checkbox-group v-model="result">
          <van-cell-group :border="false">
            <div v-for="(item, index) in list" :key="index">
              <van-cell clickable :style="{background: item.status == 'AVILABLE' ? '#EEEEEE' : '#FAFAFA'}">
                <template #title>
                  <span :style="{color: item.status == 'AVILABLE' ? '#999999' : '#222222'}">{{ item.insuredName }}</span>
                </template>
                <template #label>
                  <img src="~images/icon/idNum.png" class="img-icon" />
                  <p :style="{color: item.status == 'AVILABLE' ? '#999999' : '#222222'}">{{ item.insuredIdNum }}</p>
                </template>
                <template #right-icon>
                  <van-checkbox :name="item" :disabled="item.status == 'AVILABLE'" checked-color="#F34168" />
                </template>
              </van-cell>
            </div>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </div>

    <div class="footer">
      <div class="module">
        <read-agreement :confirmRead.sync="confirmRead" :agreementQuery="agreeUrlQuery" :color="textColor"/>
      </div>
      <van-button :disabled="result.length == 0" class="btn" @click="active">激活权益</van-button>
    </div>

  </div>
</template>
<script>
import api from '@/api/cityHealth'
import readAgreement from '@/pages/components/readAgreement'
import { getValueByKeyFromURL } from '@/utils/tools'
export default {
  name: '',
  components: { readAgreement },
  data() {
    return {
      agreeUrlQuery:{cardTypeCode:"103",keys:"sysm"},
      textColor:"#F34168",
      // 状态：AVILABLE——已激活，UNAVILABLE——未激活
      result: [],
      list: [],
      confirmRead: false, // 阅读权益
      toPath: "", // 同仁堂：toPath=trt，癌筛：toPath=asqy，我的权益：toPath=origin
    }
  },
  computed: {},
  watch: {},
  created() {
    if(getValueByKeyFromURL(location.href, 'toPath')) {
      this.toPath = getValueByKeyFromURL(location.href, 'toPath')
    } else {
      this.toPath = "origin"
    }
  },
  mounted() {
    this.result = this.list
    this.queryInsured()
  },
  methods: {
    queryInsured() {
      api.queryInsured().then((res) => {
        if(res.result.length == 0) {
          // 没有被保险人信息_查询是或否有激活卡
          this.queryCardList()
          
        } else {
          this.list = res.result
          this.result = res.result.filter(item => item.status != 'AVILABLE')
          // 所有被保人全部被激活
          if(this.list.length == res.result.filter(item => item.status == 'AVILABLE').length) {
            if(this.toPath == "trt") {
              this.$router.push("/health-atb-introTRT")
            } else {
              this.$router.push('/health-atb-origin')
            }
          }
        }
      })
    },
    // 查询权益卡列表
    queryCardList() {
      api.queryCardList({cartTypeCode: "103"}).then((res) => {
        if(res.result.length > 0) {
          if (this.toPath == "trt") {
            this.$router.push('/health-atb-introTRT')
          } else {
            this.$router.push('/health-atb-origin')
          }
        } else {
          this.$router.push({
            path: '/health-atb-activation',
            query: {
              toPath: this.toPath
            }
          })
        }
      })
    },
    // 激活权益
    active() {
      if(!this.confirmRead) {
        this.$toast('请阅读并同意下方协议')
        return;
      }
      var arr = []
      this.result.forEach(ele => {
        var obj = {}
        obj.cardTypeCode = '103'
        obj.bindName = ele.insuredName
        obj.bindIdNum = ele.insuredIdNum
        obj.bindIdType = ele.bindIdType
        arr.push(obj)
      });
      api.activateCard(arr).then(() => {
        this.getPvUv()
      })
    },
    getPvUv() {
      api.getPvUv({ eventOwner: '103', eventCode: this.toPath == 'trt' ? 'PR011' : 'PR012', eventType: 'RIGHTS_ACTIVE'}).then(() => {
        this.queryFirstCard()
      })
    },
    queryFirstCard() {
      api.queryCardList({cartTypeCode: "103"}).then((res) => {
        if(this.toPath == "trt" || this.toPath == 'asqy') {
          this.$router.push({
            path: this.toPath == "trt" ? "/health-atb-introTRT" : "/health-atb-introduction",
            query: {
              cardNo: res.result[0].cardNo
            }
          })
        } else {
          this.$router.push("/health-atb-origin")
        }
      })
    }
  },
}
</script>
<style lang="less" scoped>
.holder {
  height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  .holder_box {
    flex: 1;
    overflow-y: auto;
    padding: 0.16rem 0 0;
    .banner {
      width: 100%;
      height: 2.64rem;
      background: url('../../assets/images/atb/activate-banner.png') no-repeat;
      background-size: 100%
    }
    .insuredList {
      margin-top: -0.3rem;
      background: #FFFFFF;
      // box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
      border-radius: .16rem .16rem 0px 0px;
      padding: 0 .16rem;
      .insured-title {
        font-size: .2rem;
        font-weight: 500;
        line-height: .28rem;
        text-align: center;
        padding: .24rem 0
      }
    }
    .img-icon {
      width: 0.16rem;
      margin-right: 0.04rem;
    }
    .van-cell-group {
      padding: 0;
    }
    .van-cell {
      background: #fafafa;
      border-radius: 0.12rem;
      overflow: hidden;
      margin-bottom: 0.12rem;
      padding: 0.12rem 0.16rem;
    }
    .van-cell__title,
    .van-cell__value {
      font-size: 0.14rem;
      font-weight: 500;
      color: #222222;
      line-height: 0.2rem;
    }
    .van-cell__label {
      font-size: 0.12rem;
      color: #666666;
      font-weight: 400;
      line-height: 0.16rem;
      display: flex;
      align-items: center;
      margin-top: 0.08rem;
    }
    .van-cell::after {
      border-bottom: none
    }
  }
  .footer {
    width: 100%;
    height: 1.4rem;
    .module {
      display: flex;
      background: #fff;
      padding: 0.12rem 0.16rem;
      box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
      // border-bottom: 1px solid #F4F4F4
    }
    .btn {
      width: 3.43rem;
      height: .48rem;
      background: #F34168;
      border-radius: .24rem;
      border: none;
      margin: .06rem .16rem;
      color: #fff;
      font-size: .18rem;
      line-height: .24rem;
      font-weight: 500
    }
    /deep/.van-button--disabled {
      background: #CCCCCC !important;
      border: none !important
    }
  }
}
</style>