// 表单按扭自动固定底部样式（保单查询页、登录页等）
.buttonAutoFixedBottom() {
  display: flex;
  flex-direction: column;
  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    /deep/.van-form {
      flex: 1;
      display: flex;
      flex-direction: column;
      .form-footer {
        flex: 1;
        display: flex;
        flex-direction: column;
        &:before{
          content: '';
          flex: 1;
        }
      }
    }
  }
}

// 表单页面通用样式（如：保单查询页、登录页等）
.fromPageCommonStyle() {
  min-height: 100vh;
  overflow: hidden;
  // background: @primary-color;
  background-image: @primary-bgColor;
  // 按扭自动置底样式
  .buttonAutoFixedBottom();
  .title {
    display: flex;
    width: 100%;
    height: 0.85rem;
    justify-content: center;
    align-items: center;
    font-size: 0.3rem;
    font-weight: bold;
    color: #fff;
    img {
      height: 0.36rem;
    }
  }
  .main {
    margin: 0 0.15rem 0.3rem 0.15rem;
    /deep/.van-form {
      background: #fff;
      border-radius: 0.12rem;
      overflow: hidden;
      padding: 0.2rem 0.16rem;
      .van-cell::after {
        display: none;
      }
      .van-field {
        flex-direction: column;
        .van-field__label {
          color: #484848;
        }
        .van-field__control {
          font-size: 0.16rem;
          &::placeholder {
            font-size: 0.16rem;
          }
        }
      }
      .tips {
        color: #999;
        font-size: 0.13rem;
        margin: 0.15rem 0 0 0;
      }
      .form-footer {
        margin: 0.25rem 0 0 0;
        .van-button {
          height: 0.48rem;
          &.van-button--normal {
            font-size: 0.18rem;
          }
        }
      }
    }
  }
}